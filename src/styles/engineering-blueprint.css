/* ===================================
   Engineering Blueprint Design System
   Inspired by zed.dev aesthetic
   =================================== */

/* Import high-quality fonts similar to zed.dev */
@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;1,400&family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&display=swap');

/* ===================================
   Grid Background Pattern
   =================================== */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(37, 99, 235, 0.04) 1px, transparent 1px),
    linear-gradient(90deg, rgba(37, 99, 235, 0.04) 1px, transparent 1px);
  background-size: 24px 24px;
  background-position: -1px -1px;
}

.bg-grid-pattern-dense {
  background-image:
    linear-gradient(rgba(37, 99, 235, 0.06) 1px, transparent 1px),
    linear-gradient(90deg, rgba(37, 99, 235, 0.06) 1px, transparent 1px);
  background-size: 12px 12px;
  background-position: -1px -1px;
}

/* Dot pattern alternative */
.bg-dot-pattern {
  background-image: radial-gradient(circle, rgba(37, 99, 235, 0.08) 1px, transparent 1px);
  background-size: 24px 24px;
}

/* ===================================
   Noise Texture Overlay
   =================================== */
.noise-texture {
  position: relative;
}

.noise-texture::before {
  content: '';
  position: absolute;
  inset: 0;
  opacity: 0.025;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.85' numOctaves='3' /%3E%3C/filter%3E%3Crect width='100' height='100' filter='url(%23noise)' opacity='1'/%3E%3C/svg%3E");
  pointer-events: none;
  mix-blend-mode: multiply;
}

/* ===================================
   Blueprint Paper Background
   =================================== */
.bg-blueprint {
  background-color: #fefdfb; /* 淡米色背景 */
  background-image:
    linear-gradient(rgba(37, 99, 235, 0.04) 1px, transparent 1px),
    linear-gradient(90deg, rgba(37, 99, 235, 0.04) 1px, transparent 1px);
  background-size: 24px 24px;
  background-position: -1px -1px;
  position: relative;
}

.bg-blueprint::after {
  content: '';
  position: absolute;
  inset: 0;
  opacity: 0.03;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.85' numOctaves='3' /%3E%3C/filter%3E%3Crect width='100' height='100' filter='url(%23noise)' opacity='1'/%3E%3C/svg%3E");
  pointer-events: none;
  mix-blend-mode: multiply;
}

/* ===================================
   Technical Drawing Borders
   =================================== */
.border-technical {
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 0 0 0.5px rgba(0, 0, 0, 0.02);
}

.border-technical-dashed {
  border: 1px dashed rgba(0, 0, 0, 0.08);
}

.border-technical-dotted {
  border: 1px dotted rgba(0, 0, 0, 0.1);
}

/* Delicate dividers */
.divider-technical {
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgba(0, 0, 0, 0.06) 20%,
    rgba(0, 0, 0, 0.06) 80%,
    transparent
  );
}

/* ===================================
   Typography Enhancements
   =================================== */

/* Modern serif headings for editorial feel */
.font-serif-display {
  font-family: 'Playfair Display', 'Crimson Text', Georgia, serif;
  letter-spacing: -0.025em;
  font-feature-settings:
    'liga' 1,
    'kern' 1;
}

/* Clean sans-serif for body */
.font-sans-clean {
  font-family:
    'Inter',
    system-ui,
    -apple-system,
    sans-serif;
  letter-spacing: -0.013em;
  font-feature-settings:
    'liga' 1,
    'kern' 1;
}

/* Display headings with blueprint style */
.text-display-hero {
  font-family: 'Playfair Display', 'Crimson Text', Georgia, serif;
  font-size: clamp(2.5rem, 5vw + 1rem, 5.5rem);
  line-height: 0.9;
  letter-spacing: -0.035em;
  font-weight: 500;
  font-feature-settings:
    'liga' 1,
    'kern' 1,
    'swsh' 1;
}

.text-display-title {
  font-family: 'Playfair Display', 'Crimson Text', Georgia, serif;
  font-size: clamp(2rem, 4vw + 0.5rem, 4rem);
  line-height: 1.05;
  letter-spacing: -0.025em;
  font-weight: 500;
  font-feature-settings:
    'liga' 1,
    'kern' 1;
}

/* ===================================
   Blueprint Components
   =================================== */
/* Technical paper card */
.card-blueprint {
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow:
    0 0 0 0.5px rgba(0, 0, 0, 0.02),
    0 2px 4px rgba(0, 0, 0, 0.02),
    0 4px 8px rgba(0, 0, 0, 0.01);
  position: relative;
  overflow: hidden;
  border-radius: 0.25rem;
}

.card-blueprint::before {
  content: '';
  position: absolute;
  inset: 0;
  opacity: 0.015;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' /%3E%3C/filter%3E%3Crect width='100' height='100' filter='url(%23noise)' opacity='1'/%3E%3C/svg%3E");
  pointer-events: none;
  mix-blend-mode: multiply;
}

/* Technical button style */
.btn-technical {
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.08);
  color: var(--color-text-primary);
  font-family:
    'Inter',
    system-ui,
    -apple-system,
    sans-serif;
  font-weight: 500;
  letter-spacing: -0.011em;
  box-shadow:
    0 0 0 0.5px rgba(0, 0, 0, 0.02),
    0 1px 2px rgba(0, 0, 0, 0.04);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-technical:hover {
  border-color: rgba(0, 0, 0, 0.12);
  box-shadow:
    0 0 0 0.5px rgba(0, 0, 0, 0.04),
    0 2px 4px rgba(0, 0, 0, 0.06),
    0 4px 8px rgba(0, 0, 0, 0.02);
  transform: translateY(-1px);
}

/* ===================================
   Engineering Annotations
   =================================== */
.annotation-bracket {
  position: relative;
  padding-left: 1rem;
}

.annotation-bracket::before {
  content: '[';
  position: absolute;
  left: 0;
  top: 0;
  font-family: 'Inter', monospace;
  color: rgba(0, 0, 0, 0.3);
  font-size: 1.2em;
  line-height: 1;
}

.annotation-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.08);
  background: #ffffff;
  font-size: 0.875rem;
  font-weight: 600;
  font-family: 'JetBrains Mono', monospace;
  color: rgba(0, 0, 0, 0.7);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.annotation-number:hover {
  transform: scale(1.1);
  border-color: rgba(0, 0, 0, 0.15);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  color: rgba(0, 0, 0, 0.9);
}

/* ===================================
   Geometric Spacing Utilities
   =================================== */
.space-geometric {
  --space-unit: 8px;
}

.space-geometric > * + * {
  margin-top: calc(var(--space-unit) * 3);
}

.grid-geometric {
  display: grid;
  gap: calc(var(--space-unit) * 2);
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
}

/* ===================================
   Blueprint Shadows
   =================================== */
.shadow-technical {
  box-shadow:
    0 0 0 0.5px rgba(0, 0, 0, 0.02),
    0 1px 2px rgba(0, 0, 0, 0.04);
}

.shadow-technical-lg {
  box-shadow:
    0 0 0 0.5px rgba(0, 0, 0, 0.02),
    0 2px 4px rgba(0, 0, 0, 0.04),
    0 4px 8px rgba(0, 0, 0, 0.02);
}

.shadow-technical-xl {
  box-shadow:
    0 0 0 0.5px rgba(0, 0, 0, 0.02),
    0 4px 8px rgba(0, 0, 0, 0.04),
    0 8px 16px rgba(0, 0, 0, 0.02),
    0 16px 32px rgba(0, 0, 0, 0.01);
}

/* ===================================
   Content Typography Styles
   =================================== */
.content-prose {
  color: #475569;
  line-height: 1.8;
  font-size: 1.0625rem;
}

/* Section headers with technical style */
.section-header {
  position: relative;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

/* Technical list styling */
.technical-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.technical-list-item {
  display: flex;
  align-items: flex-start;
  padding: 0.75rem 0;
  transition: all 0.2s ease;
}

.technical-list-item:hover {
  padding-left: 0.5rem;
}

/* Enhanced card content spacing */
.card-content-spacing {
  padding: 2.5rem;
}

@media (min-width: 768px) {
  .card-content-spacing {
    padding: 3rem;
  }
}

/* ===================================
   Animation Enhancements
   =================================== */
@keyframes draw-line {
  from {
    stroke-dashoffset: 1000;
  }
  to {
    stroke-dashoffset: 0;
  }
}

.animate-draw {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: draw-line 2s ease-out forwards;
}

/* Subtle float animation for blueprint elements */
@keyframes blueprint-float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

.animate-blueprint-float {
  animation: blueprint-float 6s ease-in-out infinite;
}
