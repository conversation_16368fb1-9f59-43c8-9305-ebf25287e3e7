/* ===================================
   设计令牌系统 (Design Tokens)
   基础设计系统变量定义
   =================================== */

:root {
  /* ===================================
     间距系统 (Spacing System)
     基于8px网格系统 + 流式间距
     =================================== */
  --space-0: 0;
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 24px;
  --space-6: 32px;
  --space-7: 48px;
  --space-8: 64px;
  --space-9: 96px;
  --space-10: 128px;

  /* 流式间距 (Fluid Spacing) */
  --space-xs: clamp(4px, 0.5vw, 8px);
  --space-sm: clamp(8px, 1vw, 16px);
  --space-md: clamp(16px, 2vw, 32px);
  --space-lg: clamp(32px, 4vw, 64px);
  --space-xl: clamp(64px, 6vw, 128px);
  --space-2xl: clamp(96px, 8vw, 192px);

  /* ===================================
     字体大小 (Font Sizes)
     扩展的响应式字体系统
     =================================== */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;
  --font-size-4xl: 48px;
  --font-size-5xl: 64px;
  --font-size-6xl: 80px;
  --font-size-7xl: 96px;

  /* 流式字体大小 (Fluid Typography) */
  --font-size-fluid-sm: clamp(14px, 1.2vw, 16px);
  --font-size-fluid-base: clamp(16px, 1.4vw, 18px);
  --font-size-fluid-lg: clamp(18px, 1.8vw, 24px);
  --font-size-fluid-xl: clamp(24px, 2.5vw, 32px);
  --font-size-fluid-2xl: clamp(32px, 3.5vw, 48px);
  --font-size-fluid-3xl: clamp(48px, 5vw, 80px);

  /* 语义化字体大小 */
  --font-size-caption: var(--font-size-xs);
  --font-size-body: var(--font-size-base);
  --font-size-body-large: var(--font-size-lg);
  --font-size-subtitle: var(--font-size-xl);
  --font-size-title: var(--font-size-2xl);
  --font-size-headline: var(--font-size-3xl);
  --font-size-display: var(--font-size-4xl);
  --font-size-hero: var(--font-size-5xl);

  /* ===================================
     行高 (Line Heights)
     优化的可读性系统
     =================================== */
  --line-height-none: 1;
  --line-height-tight: 1.2;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* 语义化行高 */
  --line-height-caption: var(--line-height-snug);
  --line-height-body: var(--line-height-relaxed);
  --line-height-heading: var(--line-height-tight);
  --line-height-display: var(--line-height-none);

  /* ===================================
     字重 (Font Weights)
     =================================== */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* ===================================
     动画时长 (Animation Durations)
     完整的动画时长系统
     =================================== */
  --duration-instant: 0ms;
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 400ms;
  --duration-slower: 600ms;
  --duration-slowest: 800ms;

  /* ===================================
     动画缓动函数 (Animation Easings)
     专业的缓动曲线系统
     =================================== */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-back: cubic-bezier(0.34, 1.56, 0.64, 1);

  /* ===================================
     语义化颜色 (Semantic Colors)
     基于primary、secondary、tertiary颜色
     =================================== */

  /* 主色调 - 饱和科技蓝 (类似 zed.dev) */
  --color-primary: #2563eb;
  --color-primary-rgb: 37, 99, 235;
  --color-primary-light: #3b82f6;
  --color-primary-lighter: #60a5fa;
  --color-primary-dark: #1d4ed8;
  --color-primary-darker: #1e40af;

  /* 次要色调 - 深蓝色调 */
  --color-secondary: #1e40af;
  --color-secondary-rgb: 30, 64, 175;
  --color-secondary-light: #2563eb;
  --color-secondary-lighter: #3b82f6;
  --color-secondary-dark: #1e3a8a;
  --color-secondary-darker: #172554;

  /* 第三色调 - 用于强调和装饰元素 */
  --color-tertiary: #14b8a6;
  --color-tertiary-rgb: 20, 184, 166;
  --color-tertiary-light: #2dd4bf;
  --color-tertiary-lighter: #5eead4;
  --color-tertiary-dark: #0d9488;
  --color-tertiary-darker: #0f766e;

  /* 中性色 - 用于文本和背景 */
  --color-neutral-50: #fafafa;
  --color-neutral-100: #f4f4f5;
  --color-neutral-200: #e4e4e7;
  --color-neutral-300: #d4d4d8;
  --color-neutral-400: #a1a1aa;
  --color-neutral-500: #71717a;
  --color-neutral-600: #52525b;
  --color-neutral-700: #3f3f46;
  --color-neutral-800: #27272a;
  --color-neutral-900: #18181b;

  /* 功能性颜色 - 完整的色阶系统 */
  /* 成功色 (绿色系) */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;
  --color-success: var(--color-success-500);
  --color-success-light: var(--color-success-400);
  --color-success-dark: var(--color-success-600);

  /* 信息色 (蓝色系) */
  --color-info-50: #eff6ff;
  --color-info-100: #dbeafe;
  --color-info-200: #bfdbfe;
  --color-info-300: #93c5fd;
  --color-info-400: #60a5fa;
  --color-info-500: #3b82f6;
  --color-info-600: #2563eb;
  --color-info-700: #1d4ed8;
  --color-info-800: #1e40af;
  --color-info-900: #1e3a8a;
  --color-info: var(--color-info-500);
  --color-info-light: var(--color-info-400);
  --color-info-dark: var(--color-info-600);

  /* 警告色 (琥珀色系) */
  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;
  --color-warning: var(--color-warning-500);
  --color-warning-light: var(--color-warning-400);
  --color-warning-dark: var(--color-warning-600);

  /* 错误色 (红色系) */
  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;
  --color-error: var(--color-error-500);
  --color-error-light: var(--color-error-400);
  --color-error-dark: var(--color-error-600);

  /* 背景色系统 - 淡米色基调 */
  --color-background: #fefdfb;
  --color-background-secondary: #faf9f7;
  --color-background-tertiary: #f5f4f2;
  --color-background-muted: #f0efed;
  --color-background-elevated: #ffffff;
  --color-background-overlay: rgba(0, 0, 0, 0.8);
  --color-background-backdrop: rgba(0, 0, 0, 0.5);

  /* 文本颜色系统 - 改善对比度 */
  --color-text-primary: #1f2937;
  --color-text-secondary: #4b5563;
  --color-text-tertiary: #6b7280;
  --color-text-quaternary: #9ca3af;
  --color-text-disabled: #d1d5db;
  --color-text-inverse: #ffffff;
  --color-text-muted: var(--color-neutral-500);
  --color-text-placeholder: var(--color-neutral-400);

  /* 边框颜色系统 */
  --color-border: var(--color-neutral-200);
  --color-border-light: var(--color-neutral-100);
  --color-border-strong: var(--color-neutral-300);
  --color-border-hover: var(--color-neutral-300);
  --color-border-focus: var(--color-primary);
  --color-border-error: var(--color-error);
  --color-border-success: var(--color-success);
  --color-border-warning: var(--color-warning);

  /* 表面颜色系统 */
  --color-surface-primary: var(--color-background);
  --color-surface-secondary: var(--color-neutral-50);
  --color-surface-tertiary: var(--color-neutral-100);
  --color-surface-brand: var(--color-primary-50);
  --color-surface-success: var(--color-success-50);
  --color-surface-warning: var(--color-warning-50);
  --color-surface-error: var(--color-error-50);
  --color-surface-info: var(--color-info-50);

  /* ===================================
     圆角 (Border Radius)
     =================================== */
  --radius-none: 0;
  --radius-sm: 4px;
  --radius-base: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-full: 9999px;

  /* ===================================
     阴影系统 (Box Shadows)
     层级化的深度系统
     =================================== */
  --shadow-none: none;
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-2xl: 0 50px 100px -20px rgba(0, 0, 0, 0.25);

  /* 高级阴影 - Silicon Valley风格 */
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
  --shadow-elegant: 0 10px 25px -5px rgba(0, 0, 0, 0.08), 0 5px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-premium:
    0 20px 40px -15px rgba(0, 0, 0, 0.1), 0 10px 20px -10px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(0, 0, 0, 0.02);
  --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;

  /* 彩色阴影 */
  --shadow-primary:
    0 10px 25px -5px rgba(16, 185, 129, 0.15), 0 5px 10px -5px rgba(16, 185, 129, 0.08);
  --shadow-secondary:
    0 10px 25px -5px rgba(59, 130, 246, 0.15), 0 5px 10px -5px rgba(59, 130, 246, 0.08);
  --shadow-tertiary:
    0 10px 25px -5px rgba(20, 184, 166, 0.15), 0 5px 10px -5px rgba(20, 184, 166, 0.08);

  /* 语义化阴影 */
  --shadow-card: var(--shadow-soft);
  --shadow-card-hover: var(--shadow-elegant);
  --shadow-dropdown: var(--shadow-lg);
  --shadow-modal: var(--shadow-premium);
  --shadow-focus: 0 0 0 3px rgba(59, 130, 246, 0.15);
  --shadow-focus-error: 0 0 0 3px rgba(239, 68, 68, 0.15);
  --shadow-focus-success: 0 0 0 3px rgba(34, 197, 94, 0.15);

  /* 内阴影 */
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-inner-lg: inset 0 4px 8px 0 rgba(0, 0, 0, 0.1);
  --shadow-inner-soft: inset 0 1px 2px 0 rgba(0, 0, 0, 0.04);

  /* ===================================
     Z-Index 层级
     =================================== */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* ===================================
     导航栏高度
     =================================== */
  --navbar-height-mobile: 56px;
  --navbar-height-desktop: 64px;
}

/* ===================================
   深色模式支持
   =================================== */
@media (prefers-color-scheme: dark) {
  :root {
    /* 深色模式下的背景色 */
    --color-background: #0f0f0f;
    --color-background-secondary: var(--color-neutral-900);
    --color-background-tertiary: var(--color-neutral-800);

    /* 深色模式下的文本颜色 */
    --color-text-primary: var(--color-neutral-50);
    --color-text-secondary: var(--color-neutral-200);
    --color-text-tertiary: var(--color-neutral-400);
    --color-text-disabled: var(--color-neutral-600);

    /* 深色模式下的边框颜色 */
    --color-border: var(--color-neutral-800);
    --color-border-hover: var(--color-neutral-700);
  }
}
