<template>
  <div class="min-h-screen bg-blueprint relative">
    <!-- Background patterns -->
    <div class="absolute inset-0 bg-grid-pattern opacity-30"></div>
    <div class="absolute inset-0 bg-noise-texture opacity-20"></div>

    <!-- Hero Section -->
    <section class="py-32 relative">
      <div class="absolute inset-0 bg-dot-pattern opacity-10"></div>

      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Technical decoration -->
        <TechnicalElements
          type="corner-frame"
          :size="600"
          class="absolute inset-0 opacity-20 mx-auto"
        />

        <div class="text-center">
          <TechnicalElements
            type="cross"
            :size="48"
            class="mx-auto mb-8 text-primary-600/30"
            :animated="true"
          />

          <h1 class="text-display-hero mb-8">
            <span class="font-serif-display">Team Members</span>
          </h1>
          <p class="text-xl font-sans-clean text-slate-600 max-w-2xl mx-auto leading-relaxed">
            Meet our dedicated team members who worked together to make this project possible.
          </p>
        </div>
      </div>
    </section>

    <!-- Team Photo Section -->
    <section class="max-w-7xl mx-auto px-4 sm:px-6 mb-16 relative z-10">
      <div class="card-blueprint p-12">
        <TechnicalElements type="number" :number="1" class="mb-4" />
        <h2 class="text-display-title mb-8 font-serif-display">Our Team</h2>

        <div class="relative border-technical bg-white p-2">
          <img
            src="https://static.igem.wiki/teams/5610/wiki/teamphoto/team/team.webp"
            alt="BASIS China iGEM Team 2025"
            class="w-full h-auto object-cover"
            loading="eager"
          />
          <!-- Technical corner decorations -->
          <TechnicalElements
            type="corner-brackets"
            :size="48"
            class="absolute top-0 left-0 text-primary-600/40"
          />
          <TechnicalElements
            type="corner-brackets"
            :size="48"
            class="absolute top-0 right-0 text-primary-600/40 rotate-90"
          />
          <TechnicalElements
            type="corner-brackets"
            :size="48"
            class="absolute bottom-0 left-0 text-primary-600/40 -rotate-90"
          />
          <TechnicalElements
            type="corner-brackets"
            :size="48"
            class="absolute bottom-0 right-0 text-primary-600/40 rotate-180"
          />
        </div>

        <p class="mt-6 text-center font-sans-clean text-slate-600">
          BASIS China iGEM Team 2025 - United in our mission to tackle PFAS contamination
        </p>
      </div>
    </section>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6 relative z-10">
      <div class="card-blueprint p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
      </div>
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <div class="card-blueprint p-6">
          <TechnicalElements type="annotation" text="INDEX" class="mb-4 opacity-60" />
          <ArticleTableOfContents :items="tocItems" title="Contents" />
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 min-h-screen">
        <article class="max-w-4xl">
          <!-- Team Leaders Section -->
          <section id="team-leaders" class="mb-12">
            <div class="card-blueprint p-8">
              <TechnicalElements type="number" :number="2" class="mb-4" />
              <h2 class="text-display-subtitle mb-8 font-serif-display">Team Leaders</h2>

              <div class="scrollable-container">
                <div
                  v-for="(leader, index) in leaders"
                  :key="'leader-' + index"
                  class="member-card-blueprint group"
                >
                  <div class="photo-container">
                    <div v-if="!leader.photo" class="photo-placeholder-blueprint">
                      <User class="w-16 h-16 text-slate-400" />
                    </div>
                    <img v-else :src="leader.photo" :alt="leader.name" class="photo-image" />
                    <!-- Technical corners -->
                    <div class="absolute inset-0 pointer-events-none">
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute top-0 left-0 text-slate-400/20 group-hover:text-slate-600/30 transition-colors"
                      />
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute top-0 right-0 text-slate-400/20 group-hover:text-slate-600/30 rotate-90 transition-colors"
                      />
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute bottom-0 left-0 text-slate-400/20 group-hover:text-slate-600/30 -rotate-90 transition-colors"
                      />
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute bottom-0 right-0 text-slate-400/20 group-hover:text-slate-600/30 rotate-180 transition-colors"
                      />
                    </div>
                  </div>

                  <div class="mt-4 text-center">
                    <h3
                      class="text-lg font-serif-display text-slate-900 group-hover:text-slate-700 transition-colors"
                    >
                      {{ leader.name }}
                    </h3>
                    <p class="text-xs font-sans-clean text-slate-500 mt-2">
                      {{ leader.role }}
                    </p>
                    <p
                      v-if="leader.bio"
                      class="mt-3 text-sm font-sans-clean text-slate-600 line-clamp-3 text-center"
                    >
                      {{ leader.bio }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Team Members Section -->
          <section id="team-members" class="mb-12">
            <div class="card-blueprint p-8">
              <TechnicalElements type="number" :number="3" class="mb-4" />
              <h2 class="text-display-subtitle mb-8 font-serif-display">Team Members</h2>

              <!-- Iterate through all groups -->
              <div
                v-for="(group, groupIndex) in memberGroups"
                :key="'group-' + groupIndex"
                class="mb-12 last:mb-0"
              >
                <!-- Group title -->
                <div v-if="group.name" class="mb-4 border-b border-slate-200 pb-2 relative">
                  <h3 class="text-sm font-sans-clean text-slate-600 uppercase tracking-wide">
                    {{ group.name }}
                  </h3>
                </div>

                <!-- Group members list -->
                <div class="scrollable-container">
                  <div
                    v-for="(member, index) in group.members"
                    :key="'member-' + groupIndex + '-' + index"
                    class="member-card-blueprint group"
                  >
                    <div class="photo-container">
                      <div v-if="!member.photo" class="photo-placeholder-blueprint">
                        <User class="w-16 h-16 text-slate-400" />
                      </div>
                      <img v-else :src="member.photo" :alt="member.name" class="photo-image" />
                      <!-- Technical corners -->
                      <div class="absolute inset-0 pointer-events-none">
                        <TechnicalElements
                          type="corner-brackets"
                          :size="32"
                          class="absolute top-0 left-0 text-slate-400/20 group-hover:text-slate-600/30 transition-colors"
                        />
                        <TechnicalElements
                          type="corner-brackets"
                          :size="32"
                          class="absolute top-0 right-0 text-slate-400/20 group-hover:text-slate-600/30 rotate-90 transition-colors"
                        />
                        <TechnicalElements
                          type="corner-brackets"
                          :size="32"
                          class="absolute bottom-0 left-0 text-slate-400/20 group-hover:text-slate-600/30 -rotate-90 transition-colors"
                        />
                        <TechnicalElements
                          type="corner-brackets"
                          :size="32"
                          class="absolute bottom-0 right-0 text-slate-400/20 group-hover:text-slate-600/30 rotate-180 transition-colors"
                        />
                      </div>
                    </div>

                    <div class="mt-4 text-center">
                      <h3
                        class="text-lg font-serif-display text-slate-900 group-hover:text-slate-700 transition-colors"
                      >
                        {{ member.name }}
                      </h3>
                      <p class="text-xs font-sans-clean text-slate-500 mt-2">
                        {{ member.role }}
                      </p>
                      <p
                        v-if="member.bio"
                        class="mt-3 text-sm font-sans-clean text-slate-600 line-clamp-3 text-center"
                      >
                        {{ member.bio }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Advisors Section -->
          <section id="advisors" class="mb-12">
            <div class="card-blueprint p-8">
              <TechnicalElements type="number" :number="4" class="mb-4" />
              <h2 class="text-display-subtitle mb-8 font-serif-display">Advisors</h2>

              <div class="scrollable-container">
                <div
                  v-for="(advisor, index) in advisors"
                  :key="'advisor-' + index"
                  class="member-card-blueprint group"
                >
                  <div class="photo-container">
                    <div v-if="!advisor.photo" class="photo-placeholder-blueprint">
                      <User class="w-16 h-16 text-slate-400" />
                    </div>
                    <img v-else :src="advisor.photo" :alt="advisor.name" class="photo-image" />
                    <!-- Technical corners -->
                    <div class="absolute inset-0 pointer-events-none">
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute top-0 left-0 text-slate-400/20 group-hover:text-slate-600/30 transition-colors"
                      />
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute top-0 right-0 text-slate-400/20 group-hover:text-slate-600/30 rotate-90 transition-colors"
                      />
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute bottom-0 left-0 text-slate-400/20 group-hover:text-slate-600/30 -rotate-90 transition-colors"
                      />
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute bottom-0 right-0 text-slate-400/20 group-hover:text-slate-600/30 rotate-180 transition-colors"
                      />
                    </div>
                  </div>

                  <div class="mt-4 text-center">
                    <h3
                      class="text-lg font-serif-display text-slate-900 group-hover:text-slate-700 transition-colors"
                    >
                      {{ advisor.name }}
                    </h3>
                    <p class="text-xs font-sans-clean text-slate-500 mt-2">
                      {{ advisor.role }}
                    </p>
                    <p
                      v-if="advisor.bio"
                      class="mt-3 text-sm font-sans-clean text-slate-600 line-clamp-3 text-center"
                    >
                      {{ advisor.bio }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- PIs Section -->
          <section id="principal-investigators" class="mb-12">
            <div class="card-blueprint p-8">
              <TechnicalElements type="number" :number="5" class="mb-4" />
              <h2 class="text-display-subtitle mb-8 font-serif-display">Principal Investigators</h2>

              <div class="scrollable-container">
                <div
                  v-for="(pi, index) in pis"
                  :key="'pi-' + index"
                  class="member-card-blueprint group"
                >
                  <div class="photo-container">
                    <div v-if="!pi.photo" class="photo-placeholder-blueprint">
                      <User class="w-16 h-16 text-slate-400" />
                    </div>
                    <img v-else :src="pi.photo" :alt="pi.name" class="photo-image" />
                    <!-- Technical corners -->
                    <div class="absolute inset-0 pointer-events-none">
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute top-0 left-0 text-slate-400/20 group-hover:text-slate-600/30 transition-colors"
                      />
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute top-0 right-0 text-slate-400/20 group-hover:text-slate-600/30 rotate-90 transition-colors"
                      />
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute bottom-0 left-0 text-slate-400/20 group-hover:text-slate-600/30 -rotate-90 transition-colors"
                      />
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute bottom-0 right-0 text-slate-400/20 group-hover:text-slate-600/30 rotate-180 transition-colors"
                      />
                    </div>
                  </div>

                  <div class="mt-4 text-center">
                    <h3
                      class="text-lg font-serif-display text-slate-900 group-hover:text-slate-700 transition-colors"
                    >
                      {{ pi.name }}
                    </h3>
                    <p class="text-xs font-sans-clean text-slate-500 mt-2">
                      {{ pi.piRole }}
                    </p>
                    <p
                      v-if="pi.bio"
                      class="mt-3 text-sm font-sans-clean text-slate-600 line-clamp-3 text-center"
                    >
                      {{ pi.bio }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Instructors Section -->
          <section id="instructors" class="mb-12">
            <div class="card-blueprint p-8">
              <TechnicalElements type="number" :number="6" class="mb-4" />
              <h2 class="text-display-subtitle mb-8 font-serif-display">Instructors</h2>

              <div class="scrollable-container">
                <div
                  v-for="(instructor, index) in instructors"
                  :key="'instructor-' + index"
                  class="member-card-blueprint group"
                >
                  <div class="photo-container">
                    <div v-if="!instructor.photo" class="photo-placeholder-blueprint">
                      <User class="w-16 h-16 text-slate-400" />
                    </div>
                    <img
                      v-else
                      :src="instructor.photo"
                      :alt="instructor.name"
                      class="photo-image"
                    />
                    <!-- Technical corners -->
                    <div class="absolute inset-0 pointer-events-none">
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute top-0 left-0 text-slate-400/20 group-hover:text-slate-600/30 transition-colors"
                      />
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute top-0 right-0 text-slate-400/20 group-hover:text-slate-600/30 rotate-90 transition-colors"
                      />
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute bottom-0 left-0 text-slate-400/20 group-hover:text-slate-600/30 -rotate-90 transition-colors"
                      />
                      <TechnicalElements
                        type="corner-brackets"
                        :size="32"
                        class="absolute bottom-0 right-0 text-slate-400/20 group-hover:text-slate-600/30 rotate-180 transition-colors"
                      />
                    </div>
                  </div>

                  <div class="mt-4 text-center">
                    <h3
                      class="text-lg font-serif-display text-slate-900 group-hover:text-slate-700 transition-colors"
                    >
                      {{ instructor.name }}
                    </h3>
                    <p class="text-xs font-sans-clean text-slate-500 mt-2">
                      {{ instructor.role }}
                    </p>
                    <p
                      v-if="instructor.bio"
                      class="mt-3 text-sm font-sans-clean text-slate-600 line-clamp-3 text-center"
                    >
                      {{ instructor.bio }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'
import TechnicalElements from '@/components/ui/TechnicalElements.vue'
import teamData, { TeamGroup } from '@/data/teamMembers'
import { User } from 'lucide-vue-next'
import { computed, ref } from 'vue'

const { pis, instructors, leaders, members, advisors } = teamData

// Table of contents items
const tocItems = ref([
  { title: 'Team Leaders', url: '#team-leaders', depth: 1 },
  { title: 'Team Members', url: '#team-members', depth: 1 },
  { title: 'Advisors', url: '#advisors', depth: 1 },
  { title: 'Principal Investigators', url: '#principal-investigators', depth: 1 },
  { title: 'Instructors', url: '#instructors', depth: 1 },
])

// Group members based on TeamGroup enum
const memberGroups = computed(() => {
  // Create groups array
  const groups = []

  // Get all TeamGroup enum values as grouping basis
  const allGroups = [TeamGroup.WET, TeamGroup.DRY_LAB, TeamGroup.DESIGN_ENT, TeamGroup.HP_PROMOTION]

  // Create a group for each team group
  for (const groupName of allGroups) {
    // Find all members of this group
    const groupMembers = members.filter(m => m.group === groupName)

    if (groupMembers.length > 0) {
      // Put group leaders at the top
      // Sorting logic removed
      groups.push({
        name: groupName,
        members: groupMembers,
      })
    }
  }

  // Add members with no group assigned
  const ungroupedMembers = members.filter(m => !m.group)
  if (ungroupedMembers.length > 0) {
    groups.push({
      name: 'Other Members',
      members: ungroupedMembers,
    })
  }

  return groups
})
</script>

<style scoped>
/* Import blueprint design system */
@import '@/styles/engineering-blueprint.css';

/* Scrollable container for member cards */
.scrollable-container {
  display: flex;
  overflow-x: auto;
  padding: 2rem 1rem;
  gap: 2rem;
  scrollbar-width: thin;
  scrollbar-color: var(--color-slate-400) transparent;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Member card with blueprint styling */
.member-card-blueprint {
  padding: 1.5rem;
  background: white;
  border: 1px solid var(--color-border-technical);
  flex-shrink: 0;
  min-width: 280px;
  max-width: 320px;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.member-card-blueprint:hover {
  border-color: var(--color-slate-400);
  transform: translateY(-2px);
}

.member-card-blueprint::before {
  content: '';
  position: absolute;
  inset: -1px;
  background: linear-gradient(135deg, transparent 0%, rgba(100, 116, 139, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: -1;
}

.member-card-blueprint:hover::before {
  opacity: 1;
}

/* Photo container */
.photo-container {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  margin-bottom: 1rem;
  overflow: hidden;
  border: 1px solid var(--color-border-technical);
}

.photo-placeholder-blueprint {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    rgba(59, 130, 246, 0.03) 10px,
    rgba(59, 130, 246, 0.03) 20px
  );
}

.photo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Line clamp utility */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar styles */
.scrollable-container::-webkit-scrollbar {
  height: 8px;
}

.scrollable-container::-webkit-scrollbar-track {
  background: transparent;
}

.scrollable-container::-webkit-scrollbar-thumb {
  background: var(--color-slate-400);
  border-radius: 0;
}

.scrollable-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-slate-600);
}

/* Responsive adjustments */
@media (min-width: 640px) {
  .scrollable-container {
    gap: 2.5rem;
  }

  .member-card-blueprint {
    min-width: 300px;
    max-width: 340px;
  }
}

@media (min-width: 768px) {
  .scrollable-container {
    gap: 3rem;
  }
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}
</style>
