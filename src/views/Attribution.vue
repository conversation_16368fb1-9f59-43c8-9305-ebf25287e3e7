<template>
  <div class="min-h-screen bg-blueprint relative">
    <!-- Background patterns -->
    <div class="absolute inset-0 bg-grid-pattern opacity-30"></div>
    <div class="absolute inset-0 bg-noise-texture opacity-20"></div>

    <!-- Hero Section -->
    <section class="py-24 relative">
      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center">
          <div class="inline-flex items-center gap-2 text-sm font-mono text-slate-500 mb-6">
            <TechnicalElements type="bracket" text="CREDITS" />
          </div>

          <h1
            class="text-5xl sm:text-6xl mb-6 font-serif-display font-light tracking-tight text-slate-900"
          >
            Attribution
          </h1>
          <p class="text-lg font-sans-clean text-slate-600 max-w-2xl mx-auto leading-relaxed">
            Acknowledging the contributions and support that made our project possible
          </p>
        </div>
      </div>
    </section>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6 relative z-10">
      <div class="bg-white rounded-lg border border-slate-200 p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
      </div>
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <div class="bg-white rounded-lg border border-slate-200 p-6">
          <div class="text-xs font-mono text-slate-500 mb-4">// INDEX</div>
          <ArticleTableOfContents :items="tocItems" title="Contents" />
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 min-h-screen">
        <article class="max-w-4xl space-y-12">
          <section id="software" class="mb-12">
            <div class="bg-white rounded-lg border border-slate-200 p-8">
              <div class="flex items-center gap-3 mb-6">
                <div class="w-8 h-8 bg-slate-100 rounded flex items-center justify-center">
                  <span class="font-mono text-sm text-slate-500">01</span>
                </div>
                <h2 class="text-2xl font-serif-display text-slate-900 font-light">Software</h2>
              </div>
              <div class="space-y-8">
                <div>
                  <h3 class="text-lg font-mono text-slate-500 mb-6 flex items-center gap-2">
                    <span class="text-slate-400">//</span>
                    Development Tools
                  </h3>
                  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="tool-item group">
                      <div class="flex items-start gap-3">
                        <div
                          class="w-10 h-10 bg-slate-50 rounded flex items-center justify-center flex-shrink-0 group-hover:bg-slate-100 transition-colors"
                        >
                          <span class="text-slate-400 font-mono text-sm">01</span>
                        </div>
                        <div class="flex-1">
                          <h4 class="text-base font-medium text-slate-900 mb-1">Vue.js</h4>
                          <p class="text-sm text-slate-600 leading-relaxed">
                            Frontend framework for building user interfaces
                          </p>
                        </div>
                      </div>
                    </div>
                    <div class="tool-item group">
                      <div class="flex items-start gap-3">
                        <div
                          class="w-10 h-10 bg-slate-50 rounded flex items-center justify-center flex-shrink-0 group-hover:bg-slate-100 transition-colors"
                        >
                          <span class="text-slate-400 font-mono text-sm">02</span>
                        </div>
                        <div class="flex-1">
                          <h4 class="text-base font-medium text-slate-900 mb-1">Tailwind CSS</h4>
                          <p class="text-sm text-slate-600 leading-relaxed">
                            Utility-first CSS framework
                          </p>
                        </div>
                      </div>
                    </div>
                    <div class="tool-item group">
                      <div class="flex items-start gap-3">
                        <div
                          class="w-10 h-10 bg-slate-50 rounded flex items-center justify-center flex-shrink-0 group-hover:bg-slate-100 transition-colors"
                        >
                          <span class="text-slate-400 font-mono text-sm">03</span>
                        </div>
                        <div class="flex-1">
                          <h4 class="text-base font-medium text-slate-900 mb-1">Vite</h4>
                          <p class="text-sm text-slate-600 leading-relaxed">
                            Next generation frontend tooling
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 class="text-lg font-mono text-slate-500 mb-6 flex items-center gap-2">
                    <span class="text-slate-400">//</span>
                    Research Tools
                  </h3>
                  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="tool-item group">
                      <div class="flex items-start gap-3">
                        <div
                          class="w-10 h-10 bg-slate-50 rounded flex items-center justify-center flex-shrink-0 group-hover:bg-slate-100 transition-colors"
                        >
                          <span class="text-slate-400 font-mono text-sm">04</span>
                        </div>
                        <div class="flex-1">
                          <h4 class="text-base font-medium text-slate-900 mb-1">SnapGene</h4>
                          <p class="text-sm text-slate-600 leading-relaxed">
                            Software for designing, visualizing, and documenting molecular biology
                            experiments
                          </p>
                        </div>
                      </div>
                    </div>
                    <div class="tool-item group">
                      <div class="flex items-start gap-3">
                        <div
                          class="w-10 h-10 bg-slate-50 rounded flex items-center justify-center flex-shrink-0 group-hover:bg-slate-100 transition-colors"
                        >
                          <span class="text-slate-400 font-mono text-sm">05</span>
                        </div>
                        <div class="flex-1">
                          <h4 class="text-base font-medium text-slate-900 mb-1">MATLAB</h4>
                          <p class="text-sm text-slate-600 leading-relaxed">
                            Platform for analyzing data, developing algorithms, and creating models
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 class="text-lg font-mono text-slate-500 mb-6 flex items-center gap-2">
                    <span class="text-slate-400">//</span>
                    Design Tools
                  </h3>
                  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="tool-item group">
                      <div class="flex items-start gap-3">
                        <div
                          class="w-10 h-10 bg-slate-50 rounded flex items-center justify-center flex-shrink-0 group-hover:bg-slate-100 transition-colors"
                        >
                          <span class="text-slate-400 font-mono text-sm">06</span>
                        </div>
                        <div class="flex-1">
                          <h4 class="text-base font-medium text-slate-900 mb-1">Figma</h4>
                          <p class="text-sm text-slate-600 leading-relaxed">
                            Interface design tool
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section id="resources" class="mb-12">
            <div class="bg-white rounded-lg border border-slate-200 p-8">
              <div class="flex items-center gap-3 mb-6">
                <div class="w-8 h-8 bg-slate-100 rounded flex items-center justify-center">
                  <span class="font-mono text-sm text-slate-500">02</span>
                </div>
                <h2 class="text-2xl font-serif-display text-slate-900 font-light">Resources</h2>
              </div>
              <div class="space-y-8">
                <div>
                  <h3 class="text-lg font-mono text-slate-500 mb-6 flex items-center gap-2">
                    <span class="text-slate-400">//</span>
                    Research Papers
                  </h3>
                  <div class="space-y-3">
                    <div
                      class="flex items-start gap-3 p-4 bg-slate-50/50 rounded border border-slate-200 hover:border-slate-300 transition-all group"
                    >
                      <span class="text-slate-400 font-mono text-sm flex-shrink-0">→</span>
                      <div class="flex-1">
                        <p class="text-slate-700 group-hover:text-slate-900 transition-colors">
                          [Paper Title] - [Authors] ([Year])
                        </p>
                      </div>
                    </div>
                    <!-- Add more papers as needed -->
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section id="sponsors" class="mb-12">
            <div class="bg-white rounded-lg border border-slate-200 p-8">
              <div class="flex items-center gap-3 mb-6">
                <div class="w-8 h-8 bg-slate-100 rounded flex items-center justify-center">
                  <span class="font-mono text-sm text-slate-500">03</span>
                </div>
                <h2 class="text-2xl font-serif-display text-slate-900 font-light">Sponsors</h2>
              </div>
              <p class="text-slate-600 mb-8 leading-relaxed">
                We are grateful to our sponsors who have provided valuable support for our iGEM
                project:
              </p>
              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <div
                  class="bg-white border border-slate-200 rounded-lg p-6 hover:border-slate-300 transition-all duration-200 group"
                >
                  <div class="flex flex-col h-full">
                    <div
                      class="aspect-[3/2] bg-slate-50 rounded-lg mb-4 flex items-center justify-center p-6 group-hover:bg-slate-100 transition-colors"
                    >
                      <img
                        src="https://static.igem.wiki/teams/5610/wiki/sponsorlogo/snapgene-logo.webp"
                        alt="SnapGene Logo"
                        class="max-h-16 w-auto object-contain"
                      />
                    </div>
                    <h3 class="text-lg font-medium text-slate-900 mb-2">SnapGene</h3>
                    <p class="text-sm text-slate-600 mb-4 flex-grow leading-relaxed">
                      SnapGene provides intuitive software for molecular biology, making it easier
                      to plan, visualize, and document DNA cloning and PCR experiments.
                    </p>
                    <a
                      href="https://www.snapgene.com/"
                      target="_blank"
                      class="inline-flex items-center gap-1 text-sm text-slate-600 hover:text-slate-900 transition-colors font-medium"
                    >
                      <span>Visit Website</span>
                      <span class="text-slate-400">→</span>
                    </a>
                  </div>
                </div>
                <div
                  class="bg-white border border-slate-200 rounded-lg p-6 hover:border-slate-300 transition-all duration-200 group"
                >
                  <div class="flex flex-col h-full">
                    <div
                      class="aspect-[3/2] bg-slate-50 rounded-lg mb-4 flex items-center justify-center p-6 group-hover:bg-slate-100 transition-colors"
                    >
                      <img
                        src="https://static.igem.wiki/teams/5610/wiki/sponsorlogo/mathworks-logo-full-color-rgb.webp"
                        alt="MathWorks Logo"
                        class="max-h-16 w-auto object-contain"
                      />
                    </div>
                    <h3 class="text-lg font-medium text-slate-900 mb-2">MathWorks</h3>
                    <p class="text-sm text-slate-600 mb-4 flex-grow leading-relaxed">
                      MathWorks is the leading developer of mathematical computing software,
                      providing engineers and scientists with powerful tools for simulation,
                      modeling, and data analysis.
                    </p>
                    <a
                      href="https://www.mathworks.com/"
                      target="_blank"
                      class="inline-flex items-center gap-1 text-sm text-slate-600 hover:text-slate-900 transition-colors font-medium"
                    >
                      <span>Visit Website</span>
                      <span class="text-slate-400">→</span>
                    </a>
                  </div>
                </div>
                <!-- Add more sponsors cards here as needed -->
              </div>
            </div>
          </section>

          <section id="special-thanks" class="mb-12">
            <div class="bg-white rounded-lg border border-slate-200 p-8">
              <div class="flex items-center gap-3 mb-6">
                <div class="w-8 h-8 bg-slate-100 rounded flex items-center justify-center">
                  <span class="font-mono text-sm text-slate-500">04</span>
                </div>
                <h2 class="text-2xl font-serif-display text-slate-900 font-light">
                  Special Thanks
                </h2>
              </div>
              <p class="text-slate-600 mb-8 leading-relaxed">
                We would like to express our gratitude to the following individuals and
                organizations for their support and contributions to our project:
              </p>
              <div class="space-y-3">
                <div
                  class="flex items-start gap-3 p-4 bg-slate-50/50 rounded border border-slate-200 hover:border-slate-300 transition-all group"
                >
                  <span class="text-slate-400 font-mono text-sm flex-shrink-0">→</span>
                  <div class="flex-1">
                    <p class="text-slate-700 group-hover:text-slate-900 transition-colors">
                      [Name/Organization] - [Contribution]
                    </p>
                  </div>
                </div>
                <!-- Add more acknowledgments as needed -->
              </div>
            </div>
          </section>

          <!--
            ======================================================================
            == VERY IMPORTANT                                                   ==
            ======================================================================
            LEAVE THE IFRAME CODE BELOW AS IT IS, THE ATTRIBUTION FORM OF YOUR TEAM
            WILL BE DISPLAYED ON THIS PAGE. DO NOT REMOVE IT, OTHERWISE YOU RISK OF
            NOT MEETING BRONZE MEDAL CRITERION #2
          -->

          <section id="attribution-form" class="mb-12">
            <div class="bg-white rounded-lg border border-slate-200 p-8">
              <div class="flex items-center gap-3 mb-6">
                <div class="w-8 h-8 bg-slate-100 rounded flex items-center justify-center">
                  <span class="font-mono text-sm text-slate-500">05</span>
                </div>
                <h2 class="text-2xl font-serif-display text-slate-900 font-light">
                  Team Attribution Form
                </h2>
              </div>
              <p class="text-slate-600 mb-8 leading-relaxed">
                This form documents the contributions of each team member to our iGEM project. It
                ensures transparency and fair recognition of everyone's efforts in accordance with
                iGEM competition requirements.
              </p>
              <div class="border border-slate-200 rounded-lg overflow-hidden">
                <iframe
                  id="igem-attribution-form"
                  :src="`https://teams.igem.org/wiki/${teamID}/attributions`"
                  class="w-full min-h-[600px] sm:min-h-[700px] md:min-h-[800px]"
                  frameborder="0"
                  scrolling="yes"
                  allowfullscreen
                />
              </div>
            </div>
          </section>
          <!-- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ -->
        </article>
      </div>
    </div>
  </div>
</template>

<script setup>
// Add any necessary imports and logic here
import { ref } from 'vue'
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'
import TechnicalElements from '@/components/ui/TechnicalElements.vue'
import { ChevronRight } from 'lucide-vue-next'

// Get team ID from environment variable
const teamID = ref(import.meta.env.VITE_TEAM_ID || '5610')

// Table of contents items
const tocItems = ref([
  { title: 'Software', url: '#software', depth: 1 },
  { title: 'Resources', url: '#resources', depth: 1 },
  { title: 'Sponsors', url: '#sponsors', depth: 1 },
  { title: 'Special Thanks', url: '#special-thanks', depth: 1 },
  { title: 'Attribution Form', url: '#attribution-form', depth: 1 },
])
</script>

<style scoped>
/* Import blueprint design system */
@import '@/styles/engineering-blueprint.css';

.tool-item {
  padding: 1.25rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.tool-item:hover {
  border-color: #94a3b8;
  background-color: #fafafa;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}
</style>
