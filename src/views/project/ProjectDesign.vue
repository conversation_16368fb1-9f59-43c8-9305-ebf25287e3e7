<template>
  <div class="min-h-screen bg-blueprint relative">
    <!-- Background patterns -->
    <div class="absolute inset-0 bg-grid-pattern opacity-30"></div>
    <div class="absolute inset-0 bg-noise-texture opacity-20"></div>

    <!-- Hero Section -->
    <section class="py-32 relative">
      <div class="absolute inset-0 bg-dot-pattern opacity-10"></div>

      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Technical decoration -->
        <TechnicalElements
          type="corner-frame"
          :size="600"
          class="absolute inset-0 opacity-20 mx-auto"
        />

        <div class="text-center">
          <TechnicalElements
            type="cross"
            :size="48"
            class="mx-auto mb-8 text-primary-600/30"
            :animated="true"
          />

          <h1 class="text-display-hero mb-8">
            <span class="font-serif-display">Project Design</span>
          </h1>
          <p class="text-xl font-sans-clean text-slate-600 max-w-2xl mx-auto leading-relaxed">
            Our project design process and methodology for synthetic biology applications.
          </p>
        </div>
      </div>
    </section>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6 relative z-10">
      <div class="card-blueprint p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
      </div>
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <div class="card-blueprint p-6">
          <TechnicalElements type="annotation" text="INDEX" class="mb-4 opacity-60" />
          <ArticleTableOfContents :items="tocItems" title="Contents" />
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 min-h-screen">
        <article class="max-w-4xl space-y-12">
          <section id="overview" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="1" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Project Overview
              </h2>
              <p class="content-prose">
                Our project focuses on developing a novel biosensor system for detecting
                environmental pollutants. The design incorporates synthetic biology principles and
                advanced molecular engineering techniques.
              </p>
            </div>
          </section>

          <section id="mathematical-model" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="2" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Mathematical Model
              </h2>
              <p class="content-prose">
                The kinetics of our biosensor can be described by the following differential
                equation:
              </p>
              <div class="bg-slate-900/5 border border-technical p-6 mb-6 overflow-x-auto">
                <KatexMath
                  expression="\frac{dP}{dt} = k_p[E][S] - k_d[P]"
                  :display="true"
                  class="text-xl"
                />
              </div>
              <p class="content-prose text-base">Where:</p>
              <ul class="technical-list mb-8">
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    <KatexMath expression="P" />
                    is the product concentration
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    <KatexMath expression="E" />
                    is the enzyme concentration
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    <KatexMath expression="S" />
                    is the substrate concentration
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    <KatexMath expression="k_p" />
                    is the production rate constant
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    <KatexMath expression="k_d" />
                    is the degradation rate constant
                  </span>
                </li>
              </ul>
            </div>
          </section>

          <section id="design-principles" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="3" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Design Principles
              </h2>

              <div class="space-y-8">
                <div>
                  <h3
                    id="modularity"
                    class="section-header text-xl font-serif-display text-slate-800 mt-8"
                  >
                    Modularity
                  </h3>
                  <p class="content-prose text-base">
                    Our design follows a modular approach, allowing for easy component replacement
                    and system optimization. The key modules include:
                  </p>
                  <ul class="technical-list mb-8">
                    <li class="technical-list-item group">
                      <span class="text-primary-600 mr-3 mt-0.5 flex-shrink-0">
                        <ChevronRight class="w-4 h-4" />
                      </span>
                      <span
                        class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                      >
                        Sensing module
                      </span>
                    </li>
                    <li class="technical-list-item group">
                      <span class="text-primary-600 mr-3 mt-0.5 flex-shrink-0">
                        <ChevronRight class="w-4 h-4" />
                      </span>
                      <span
                        class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                      >
                        Processing module
                      </span>
                    </li>
                    <li class="technical-list-item group">
                      <span class="text-primary-600 mr-3 mt-0.5 flex-shrink-0">
                        <ChevronRight class="w-4 h-4" />
                      </span>
                      <span
                        class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                      >
                        Output module
                      </span>
                    </li>
                  </ul>

                  <h3
                    id="optimization"
                    class="section-header text-xl font-serif-display text-slate-800 mt-8"
                  >
                    Optimization
                  </h3>
                  <p class="content-prose text-base">
                    The system's performance is optimized through:
                  </p>
                  <div class="bg-slate-900/5 border border-technical p-6 mb-6 overflow-x-auto">
                    <KatexMath
                      expression="\max_{x,y} f(x,y) = \frac{k_{cat}[E_t][S]}{K_M + [S]}"
                      :display="true"
                      class="text-xl"
                    />
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section id="implementation" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="4" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Implementation
              </h2>
              <p class="content-prose">The implementation process involves several key steps:</p>
              <ol class="list-decimal pl-6 space-y-3 text-base font-sans-clean text-slate-700 mb-6">
                <li>Gene synthesis and assembly</li>
                <li>Protein expression optimization</li>
                <li>System integration and testing</li>
              </ol>
            </div>
          </section>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import KatexMath from '@/components/KatexMath.vue'
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'
import TechnicalElements from '@/components/ui/TechnicalElements.vue'
import { ChevronRight } from 'lucide-vue-next'

// Using the standard TOC item structure
const tocItems = ref([
  { title: 'Project Overview', url: '#overview', depth: 1 },
  { title: 'Mathematical Model', url: '#mathematical-model', depth: 1 },
  { title: 'Design Principles', url: '#design-principles', depth: 1 },
  { title: 'Modularity', url: '#modularity', depth: 2 },
  { title: 'Optimization', url: '#optimization', depth: 2 },
  { title: 'Implementation', url: '#implementation', depth: 1 },
])
</script>

<style scoped>
/* Import blueprint design system */
@import '@/styles/engineering-blueprint.css';

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}
</style>
