<template>
  <div class="min-h-screen bg-white relative">
    <!-- Blueprint grid background -->
    <div class="absolute inset-0 bg-grid-pattern opacity-30"></div>
    <div class="absolute inset-0 bg-noise-texture opacity-10"></div>

    <!-- Main content -->
    <section class="relative min-h-screen flex items-center justify-center px-4">
      <div class="max-w-4xl mx-auto text-center">
        <!-- Technical 404 display -->
        <div class="mb-12 relative inline-block">
          <!-- Large 404 number -->
          <h1
            class="text-[8rem] sm:text-[10rem] md:text-[12rem] font-mono font-light text-primary-100 leading-none select-none"
          >
            404
          </h1>

          <!-- Technical overlay text -->
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="border-technical bg-white px-8 py-4">
              <span class="text-2xl font-serif-display text-slate-800 uppercase tracking-wider">
                Not Found
              </span>
            </div>
          </div>

          <!-- Technical corner marks -->
          <div class="absolute top-0 left-0 w-4 h-4 border-l-2 border-t-2 border-slate-400"></div>
          <div class="absolute top-0 right-0 w-4 h-4 border-r-2 border-t-2 border-slate-400"></div>
          <div
            class="absolute bottom-0 left-0 w-4 h-4 border-l-2 border-b-2 border-slate-400"
          ></div>
          <div
            class="absolute bottom-0 right-0 w-4 h-4 border-r-2 border-b-2 border-slate-400"
          ></div>
        </div>

        <!-- Error message -->
        <div class="mb-12 max-w-2xl mx-auto">
          <div class="relative">
            <TechnicalElements type="divider" class="mb-8 opacity-50" />

            <h2 class="text-2xl font-serif-display text-slate-800 mb-4 font-light">
              Page Not Found
            </h2>

            <p class="text-base font-sans-clean text-slate-600 leading-relaxed">
              The requested resource could not be located in our system. Please verify the URL or
              navigate to an existing section.
            </p>

            <TechnicalElements type="divider" class="mt-8 opacity-50" />
          </div>
        </div>

        <!-- Navigation options -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
          <router-link
            to="/"
            class="group inline-flex items-center justify-center px-8 py-3 text-sm font-sans-clean bg-primary-600 text-white border border-primary-600 hover:bg-primary-700 transition-all duration-200 uppercase tracking-wider"
          >
            <Home class="w-4 h-4 mr-3" />
            <span>Return Home</span>
          </router-link>

          <button
            @click="goBack"
            class="group inline-flex items-center justify-center px-8 py-3 text-sm font-sans-clean bg-white text-slate-700 border-technical hover:border-slate-400 transition-all duration-200 uppercase tracking-wider"
          >
            <ArrowLeft class="w-4 h-4 mr-3" />
            <span>Go Back</span>
          </button>
        </div>

        <!-- Decorative elements -->
        <div class="mt-20 mb-16">
          <TechnicalElements type="divider" class="max-w-xs mx-auto opacity-30" />
        </div>

        <!-- Quick links -->
        <div class="flex flex-wrap justify-center gap-6 text-sm font-sans-clean">
          <router-link to="/" class="text-slate-500 hover:text-primary-600 transition-colors">
            Home
          </router-link>
          <span class="text-slate-300">·</span>
          <router-link
            to="/project/overview"
            class="text-slate-500 hover:text-primary-600 transition-colors"
          >
            Project
          </router-link>
          <span class="text-slate-300">·</span>
          <router-link
            to="/wet-lab/experiment"
            class="text-slate-500 hover:text-primary-600 transition-colors"
          >
            Lab Work
          </router-link>
          <span class="text-slate-300">·</span>
          <router-link
            to="/teammembers"
            class="text-slate-500 hover:text-primary-600 transition-colors"
          >
            Team
          </router-link>
          <span class="text-slate-300">·</span>
          <router-link
            to="/human-practices"
            class="text-slate-500 hover:text-primary-600 transition-colors"
          >
            Human Practices
          </router-link>
        </div>

        <!-- Technical footer -->
        <div class="mt-16">
          <code class="text-xs font-mono text-slate-400">
            HTTP/1.1 404 Not Found | {{ timestamp }}
          </code>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { computed } from 'vue'
import { Home, ArrowLeft } from 'lucide-vue-next'
import TechnicalElements from '@/components/ui/TechnicalElements.vue'

const router = useRouter()

// Go back to previous page
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

// Generate timestamp
const timestamp = computed(() => {
  return new Date().toISOString()
})
</script>

<style scoped>
/* Import blueprint styles */
@import '@/styles/engineering-blueprint.css';

/* Focus states */
a:focus-visible,
button:focus-visible {
  outline: 1px solid theme('colors.slate.600');
  outline-offset: -1px;
}

/* Ensure full height */
section {
  min-height: 100vh;
}
</style>
