<template>
  <div class="min-h-screen bg-blueprint relative">
    <!-- Background patterns -->
    <div class="absolute inset-0 bg-grid-pattern opacity-30"></div>
    <div class="absolute inset-0 bg-noise-texture opacity-20"></div>

    <!-- Hero Section -->
    <section class="py-32 relative">
      <div class="absolute inset-0 bg-dot-pattern opacity-10"></div>

      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Technical decoration -->
        <TechnicalElements
          type="corner-frame"
          :size="600"
          class="absolute inset-0 opacity-20 mx-auto"
        />

        <div class="text-center">
          <TechnicalElements
            type="cross"
            :size="48"
            class="mx-auto mb-8 text-primary-600/30"
            :animated="true"
          />

          <h1 class="text-display-hero mb-8">
            <span class="font-serif-display">Software Development</span>
          </h1>
          <p class="text-xl font-sans-clean text-slate-600 max-w-2xl mx-auto leading-relaxed">
            Our software solutions for data analysis, visualization, and system control.
          </p>
        </div>
      </div>
    </section>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6 relative z-10">
      <div class="card-blueprint p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
      </div>
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <div class="card-blueprint p-6">
          <TechnicalElements type="annotation" text="INDEX" class="mb-4 opacity-60" />
          <ArticleTableOfContents :items="tocItems" title="Contents" />
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 min-h-screen">
        <article class="max-w-4xl space-y-12">
          <section id="overview" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="1" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">Overview</h2>
              <p class="content-prose">
                Our software platform provides comprehensive tools for biosensor data analysis,
                visualization, and system control.
              </p>
            </div>
          </section>

          <section id="architecture" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="2" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                System Architecture
              </h2>

              <h3
                id="backend"
                class="section-header text-xl font-serif-display text-slate-800 mt-8 mb-4"
              >
                Backend Services
              </h3>
              <p class="content-prose text-base mb-6">Key backend components:</p>
              <ul class="technical-list mb-8 mb-6">
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Data processing pipeline
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    RESTful API endpoints
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Database management
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Authentication system
                  </span>
                </li>
              </ul>

              <h3
                id="frontend"
                class="section-header text-xl font-serif-display text-slate-800 mt-8 mb-4"
              >
                Frontend Interface
              </h3>
              <p class="content-prose text-base mb-6">User interface features:</p>
              <ul class="technical-list mb-8">
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Real-time data visualization
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Interactive analysis tools
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Responsive design
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Intuitive controls
                  </span>
                </li>
              </ul>
            </div>
          </section>

          <section id="data-processing" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="3" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Data Processing
              </h2>

              <h3
                id="signal-processing"
                class="section-header text-xl font-serif-display text-slate-800 mt-8 mb-4"
              >
                Signal Processing
              </h3>
              <p class="content-prose text-base mb-6">Signal processing algorithm:</p>
              <div class="bg-gray-50 p-6 rounded-lg shadow-sm mb-6 overflow-x-auto">
                <KatexMath
                  expression="y[n] = \sum_{k=0}^{M-1} h[k]x[n-k]"
                  :display="true"
                  class="text-xl"
                />
              </div>

              <h3
                id="analysis"
                class="section-header text-xl font-serif-display text-slate-800 mt-8 mb-4"
              >
                Data Analysis
              </h3>
              <p class="content-prose text-base mb-6">Statistical analysis methods:</p>
              <div class="bg-gray-50 p-6 rounded-lg shadow-sm mb-6 overflow-x-auto">
                <KatexMath
                  expression="\sigma = \sqrt{\frac{1}{N-1}\sum_{i=1}^N (x_i - \bar{x})^2}"
                  :display="true"
                  class="text-xl"
                />
              </div>
            </div>
          </section>

          <section id="visualization" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="4" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Data Visualization
              </h2>
              <p class="content-prose mb-6">Our visualization tools include:</p>
              <ul class="technical-list mb-8">
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Time series plots
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    3D surface visualization
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Interactive dashboards
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Custom chart types
                  </span>
                </li>
              </ul>
            </div>
          </section>

          <section id="deployment" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="5" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">Deployment</h2>
              <p class="content-prose mb-6">Our deployment strategy includes:</p>
              <ul class="technical-list mb-8">
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Continuous Integration/Deployment
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Automated testing
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Version control
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Documentation
                  </span>
                </li>
              </ul>
            </div>
          </section>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'
import TechnicalElements from '@/components/ui/TechnicalElements.vue'
import KatexMath from '@/components/KatexMath.vue'
import { ChevronRight } from 'lucide-vue-next'

// Define component name to satisfy ESLint multi-word component name rule
defineOptions({
  name: 'SoftwareDevelopment',
})

const _mobileNavOpen = ref(false)

// Using the standard TOC item structure
const tocItems = ref([
  { title: 'Overview', url: '#overview', depth: 1 },
  { title: 'System Architecture', url: '#architecture', depth: 1 },
  { title: 'Backend Services', url: '#backend', depth: 2 },
  { title: 'Frontend Interface', url: '#frontend', depth: 2 },
  { title: 'Data Processing', url: '#data-processing', depth: 1 },
  { title: 'Signal Processing', url: '#signal-processing', depth: 2 },
  { title: 'Data Analysis', url: '#analysis', depth: 2 },
  { title: 'Data Visualization', url: '#visualization', depth: 1 },
  { title: 'Deployment', url: '#deployment', depth: 1 },
])
</script>

<style scoped>
/* Import blueprint design system */
@import '@/styles/engineering-blueprint.css';

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}
</style>
