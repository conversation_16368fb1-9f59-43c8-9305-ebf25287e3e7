<template>
  <div class="min-h-screen bg-blueprint relative">
    <!-- Background patterns -->
    <div class="absolute inset-0 bg-grid-pattern opacity-30"></div>
    <div class="absolute inset-0 bg-noise-texture opacity-20"></div>

    <!-- Hero Section -->
    <section class="py-32 relative">
      <div class="absolute inset-0 bg-dot-pattern opacity-10"></div>

      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Technical decoration -->
        <TechnicalElements
          type="corner-frame"
          :size="600"
          class="absolute inset-0 opacity-20 mx-auto"
        />

        <div class="text-center">
          <TechnicalElements
            type="cross"
            :size="48"
            class="mx-auto mb-8 text-primary-600/30"
            :animated="true"
          />

          <h1 class="text-display-hero mb-8">
            <span class="font-serif-display">Mathematical Modeling</span>
          </h1>
          <p class="text-xl font-sans-clean text-slate-600 max-w-2xl mx-auto leading-relaxed">
            Computational and mathematical models that describe and predict our system's behavior.
          </p>
        </div>
      </div>
    </section>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6 relative z-10">
      <div class="card-blueprint p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
      </div>
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <div class="card-blueprint p-6">
          <TechnicalElements type="annotation" text="INDEX" class="mb-4 opacity-60" />
          <ArticleTableOfContents :items="tocItems" title="Contents" />
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 min-h-screen">
        <article class="max-w-4xl space-y-12">
          <section id="overview" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="1" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Model Overview
              </h2>
              <p class="content-prose">
                Our computational model integrates multiple components to predict and optimize the
                biosensor system's behavior.
              </p>
            </div>
          </section>

          <section id="mathematical-framework" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="2" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Mathematical Framework
              </h2>

              <h3
                id="reaction-kinetics"
                class="section-header text-xl font-serif-display text-slate-800 mt-8 mb-4"
              >
                Reaction Kinetics
              </h3>
              <p class="content-prose text-base mb-6">
                The core reaction kinetics are described by:
              </p>
              <div class="bg-slate-900/5 border border-technical p-6 mb-6 overflow-x-auto">
                <KatexMath
                  expression="\frac{d[P]}{dt} = k_{cat}[E][S] - k_d[P]"
                  :display="true"
                  class="text-xl"
                />
              </div>

              <h3
                id="diffusion-model"
                class="section-header text-xl font-serif-display text-slate-800 mt-8 mb-4"
              >
                Diffusion Model
              </h3>
              <p class="content-prose text-base mb-6">
                Spatial distribution is modeled using the diffusion equation:
              </p>
              <div class="bg-slate-900/5 border border-technical p-6 mb-6 overflow-x-auto">
                <KatexMath
                  expression="\frac{\partial C}{\partial t} = D\nabla^2C + R(C)"
                  :display="true"
                  class="text-xl"
                />
              </div>
            </div>
          </section>

          <section id="numerical-methods" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="3" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Numerical Methods
              </h2>

              <h3
                id="discretization"
                class="section-header text-xl font-serif-display text-slate-800 mt-8 mb-4"
              >
                Discretization
              </h3>
              <p class="content-prose text-base mb-6">Finite difference approximation:</p>
              <div class="bg-slate-900/5 border border-technical p-6 mb-6 overflow-x-auto">
                <KatexMath
                  expression="\frac{\partial^2 C}{\partial x^2} \approx \frac{C_{i+1} - 2C_i + C_{i-1}}{\Delta x^2}"
                  :display="true"
                  class="text-xl"
                />
              </div>

              <h3
                id="solver"
                class="section-header text-xl font-serif-display text-slate-800 mt-8 mb-4"
              >
                Numerical Solver
              </h3>
              <p class="content-prose text-base mb-6">Key features of our numerical solver:</p>
              <ul class="technical-list mb-8">
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Adaptive time stepping
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Error control mechanisms
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Stability analysis
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Convergence criteria
                  </span>
                </li>
              </ul>
            </div>
          </section>

          <section id="optimization" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="4" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                System Optimization
              </h2>
              <p class="content-prose mb-6">Optimization objective function:</p>
              <div class="bg-slate-900/5 border border-technical p-6 mb-6 overflow-x-auto">
                <KatexMath
                  expression="\min_{\theta} \sum_{i=1}^n (y_i - f(x_i; \theta))^2 + \lambda\|\theta\|_2^2"
                  :display="true"
                  class="text-xl"
                />
              </div>
            </div>
          </section>

          <section id="validation" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="5" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Model Validation
              </h2>
              <p class="content-prose mb-6">Model validation metrics:</p>
              <ul class="technical-list mb-8">
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    R² score: > 0.95
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Mean absolute error: &lt; 5%
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Cross-validation results
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Residual analysis
                  </span>
                </li>
              </ul>
            </div>
          </section>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'
import TechnicalElements from '@/components/ui/TechnicalElements.vue'
import KatexMath from '@/components/KatexMath.vue'
import { ChevronRight } from 'lucide-vue-next'

// Using the standard TOC item structure
const tocItems = ref([
  { title: 'Model Overview', url: '#overview', depth: 1 },
  { title: 'Mathematical Framework', url: '#mathematical-framework', depth: 1 },
  { title: 'Reaction Kinetics', url: '#reaction-kinetics', depth: 2 },
  { title: 'Diffusion Model', url: '#diffusion-model', depth: 2 },
  { title: 'Numerical Methods', url: '#numerical-methods', depth: 1 },
  { title: 'Discretization', url: '#discretization', depth: 2 },
  { title: 'Numerical Solver', url: '#solver', depth: 2 },
  { title: 'System Optimization', url: '#optimization', depth: 1 },
  { title: 'Model Validation', url: '#validation', depth: 1 },
])
</script>

<style scoped>
/* Import blueprint design system */
@import '@/styles/engineering-blueprint.css';

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}
</style>
