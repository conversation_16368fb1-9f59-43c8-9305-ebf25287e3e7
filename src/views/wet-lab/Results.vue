<template>
  <div class="min-h-screen bg-blueprint relative">
    <!-- Background patterns -->
    <div class="absolute inset-0 bg-grid-pattern opacity-30"></div>
    <div class="absolute inset-0 bg-noise-texture opacity-20"></div>

    <!-- Hero Section -->
    <section class="py-32 relative">
      <div class="absolute inset-0 bg-dot-pattern opacity-10"></div>

      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Technical decoration -->
        <TechnicalElements
          type="corner-frame"
          :size="600"
          class="absolute inset-0 opacity-20 mx-auto"
        />

        <div class="text-center">
          <TechnicalElements
            type="cross"
            :size="48"
            class="mx-auto mb-8 text-primary-600/30"
            :animated="true"
          />

          <h1 class="text-display-hero mb-8">
            <span class="font-serif-display">Experimental Results</span>
          </h1>
          <p class="text-xl font-sans-clean text-slate-600 max-w-2xl mx-auto leading-relaxed">
            Our project's experimental data, analysis, and conclusions.
          </p>
        </div>
      </div>
    </section>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6 relative z-10">
      <div class="card-blueprint p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
      </div>
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <div class="card-blueprint p-6">
          <TechnicalElements type="annotation" text="INDEX" class="mb-4 opacity-60" />
          <ArticleTableOfContents :items="tocItems" title="Contents" />
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 min-h-screen">
        <article class="max-w-4xl space-y-12">
          <section id="methods" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="1" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">Methods</h2>
              <p class="content-prose">
                We employed a range of standard and innovative experimental methods to validate our
                hypotheses.
              </p>

              <h3
                id="strains"
                class="section-header text-xl font-serif-display text-slate-800 mt-8"
              >
                Strain Construction
              </h3>
              <p class="content-prose text-base">
                We used E. coli DH5α as our primary experimental strain and constructed expression
                systems using standard molecular cloning techniques.
              </p>

              <h3
                id="protocols"
                class="section-header text-xl font-serif-display text-slate-800 mt-8"
              >
                Experimental Protocols
              </h3>
              <p class="content-prose text-base">
                All experiments were conducted according to standard operating procedures and in
                compliance with biosafety regulations.
              </p>
            </div>
          </section>

          <section id="data-analysis" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="2" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Data Analysis
              </h2>
              <p class="content-prose">
                We used R and Python for data analysis and visualization, applying appropriate
                statistical methods for all analyses.
              </p>
            </div>
          </section>

          <section id="results" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="3" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Key Results
              </h2>
              <p class="content-prose">
                Our experimental results support our initial hypotheses, demonstrating that our
                designed system effectively responds to target molecules.
              </p>

              <h3
                id="system-performance"
                class="section-header text-xl font-serif-display text-slate-800 mt-8"
              >
                System Performance
              </h3>
              <p class="content-prose text-base">
                Under optimal conditions, our system demonstrated high sensitivity and specificity,
                with a detection limit as low as 10 nM.
              </p>

              <h3
                id="optimization-results"
                class="section-header text-xl font-serif-display text-slate-800 mt-8"
              >
                Optimization Results
              </h3>
              <p class="content-prose text-base">
                Through parameter optimization, we reduced system response time from 24 hours to 6
                hours.
              </p>
            </div>
          </section>

          <section id="discussion" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="4" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">Discussion</h2>
              <p class="content-prose">
                Our results indicate that synthetic biology methods can be effectively applied to
                environmental monitoring and biosensor development.
              </p>
            </div>
          </section>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'
import TechnicalElements from '@/components/ui/TechnicalElements.vue'

// Using the standard TOC item structure
const tocItems = ref([
  { title: 'Methods', url: '#methods', depth: 1 },
  { title: 'Strain Construction', url: '#strains', depth: 2 },
  { title: 'Experimental Protocols', url: '#protocols', depth: 2 },
  { title: 'Data Analysis', url: '#data-analysis', depth: 1 },
  { title: 'Key Results', url: '#results', depth: 1 },
  { title: 'System Performance', url: '#system-performance', depth: 2 },
  { title: 'Optimization Results', url: '#optimization-results', depth: 2 },
  { title: 'Discussion', url: '#discussion', depth: 1 },
])
</script>

<style scoped>
/* Import blueprint design system */
@import '@/styles/engineering-blueprint.css';

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}
</style>
