<template>
  <div class="min-h-screen bg-blueprint relative">
    <!-- Background patterns -->
    <div class="absolute inset-0 bg-grid-pattern opacity-30"></div>
    <div class="absolute inset-0 bg-noise-texture opacity-20"></div>

    <!-- Hero Section -->
    <section class="py-32 relative">
      <div class="absolute inset-0 bg-dot-pattern opacity-10"></div>

      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Technical decoration -->
        <TechnicalElements
          type="corner-frame"
          :size="600"
          class="absolute inset-0 opacity-20 mx-auto"
        />

        <div class="text-center">
          <TechnicalElements
            type="cross"
            :size="48"
            class="mx-auto mb-8 text-primary-600/30"
            :animated="true"
          />

          <h1 class="text-display-hero mb-8">
            <span class="font-serif-display">BioBrick Parts</span>
          </h1>
          <p class="text-xl font-sans-clean text-slate-600 max-w-2xl mx-auto leading-relaxed">
            The genetic parts and components used in our synthetic biology project.
          </p>
        </div>
      </div>
    </section>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6 relative z-10">
      <div class="card-blueprint p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
      </div>
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <div class="card-blueprint p-6">
          <TechnicalElements type="annotation" text="INDEX" class="mb-4 opacity-60" />
          <ArticleTableOfContents :items="tocItems" title="Contents" />
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 min-h-screen">
        <article class="max-w-4xl space-y-12">
          <section id="overview" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="1" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">Overview</h2>
              <p class="content-prose">
                Our project utilizes various standardized biological parts to create a functional
                biosensor system. Each part has been carefully selected and characterized.
              </p>
            </div>
          </section>

          <section id="promoters" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="2" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Promoter Systems
              </h2>

              <h3
                id="constitutive"
                class="section-header text-xl font-serif-display text-slate-800 mt-8"
              >
                Constitutive Promoters
              </h3>
              <p class="content-prose text-base">Expression strength of constitutive promoters:</p>
              <div class="bg-slate-900/5 border border-technical p-6 mb-6 overflow-x-auto">
                <KatexMath
                  expression="P_{strength} = \alpha \cdot [RNA_{polymerase}] \cdot k_{binding}"
                  :display="true"
                  class="text-xl"
                />
              </div>

              <h3
                id="inducible"
                class="section-header text-xl font-serif-display text-slate-800 mt-8"
              >
                Inducible Promoters
              </h3>
              <p class="content-prose text-base">Key characteristics of our inducible promoters:</p>
              <ul class="technical-list mb-8">
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Tight regulation
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Low basal expression
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    High induction ratio
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Rapid response time
                  </span>
                </li>
              </ul>
            </div>
          </section>

          <section id="coding-sequences" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="3" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Coding Sequences
              </h2>

              <h3
                id="reporters"
                class="section-header text-xl font-serif-display text-slate-800 mt-8"
              >
                Reporter Proteins
              </h3>
              <p class="content-prose text-base">Our reporter system includes:</p>
              <ul class="technical-list mb-8">
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Fluorescent proteins (GFP, RFP)
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Luminescent proteins
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Colorimetric enzymes
                  </span>
                </li>
              </ul>

              <h3
                id="functional"
                class="section-header text-xl font-serif-display text-slate-800 mt-8"
              >
                Functional Proteins
              </h3>
              <p class="content-prose text-base">Key functional proteins in our system:</p>
              <ul class="technical-list mb-8">
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Sensor proteins
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Signal transducers
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Regulatory elements
                  </span>
                </li>
              </ul>
            </div>
          </section>

          <section id="terminators" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="4" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Terminators
              </h2>
              <p class="content-prose">Terminator efficiency is calculated as:</p>
              <div class="bg-slate-900/5 border border-technical p-6 mb-6 overflow-x-auto">
                <KatexMath
                  expression="Efficiency = \left(1 - \frac{Readthrough}{Total\,Transcription}\right) \times 100\%"
                  :display="true"
                  class="text-xl"
                />
              </div>
            </div>
          </section>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'
import TechnicalElements from '@/components/ui/TechnicalElements.vue'
import KatexMath from '@/components/KatexMath.vue'
import { ChevronRight } from 'lucide-vue-next'

// Using the standard TOC item structure
const tocItems = ref([
  { title: 'Overview', url: '#overview', depth: 1 },
  { title: 'Promoter Systems', url: '#promoters', depth: 1 },
  { title: 'Constitutive Promoters', url: '#constitutive', depth: 2 },
  { title: 'Inducible Promoters', url: '#inducible', depth: 2 },
  { title: 'Coding Sequences', url: '#coding-sequences', depth: 1 },
  { title: 'Reporter Proteins', url: '#reporters', depth: 2 },
  { title: 'Functional Proteins', url: '#functional', depth: 2 },
  { title: 'Terminators', url: '#terminators', depth: 1 },
])
</script>

<style scoped>
/* Import blueprint design system */
@import '@/styles/engineering-blueprint.css';

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}
</style>
