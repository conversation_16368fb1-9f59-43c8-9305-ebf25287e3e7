<template>
  <div class="min-h-screen bg-blueprint relative">
    <!-- Background patterns -->
    <div class="absolute inset-0 bg-grid-pattern opacity-30"></div>
    <div class="absolute inset-0 bg-noise-texture opacity-20"></div>

    <!-- Hero Section -->
    <section class="py-32 relative">
      <div class="absolute inset-0 bg-dot-pattern opacity-10"></div>

      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Technical decoration -->
        <TechnicalElements
          type="corner-frame"
          :size="600"
          class="absolute inset-0 opacity-20 mx-auto"
        />

        <div class="text-center">
          <TechnicalElements
            type="cross"
            :size="48"
            class="mx-auto mb-8 text-primary-600/30"
            :animated="true"
          />

          <h1 class="text-display-hero mb-8">
            <span class="font-serif-display">Engineering Success</span>
          </h1>
          <p class="text-xl font-sans-clean text-slate-600 max-w-2xl mx-auto leading-relaxed">
            Our achievements, optimization processes, and solutions to engineering challenges.
          </p>
        </div>
      </div>
    </section>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6 relative z-10">
      <div class="card-blueprint p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
      </div>
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <div class="card-blueprint p-6">
          <TechnicalElements type="annotation" text="INDEX" class="mb-4 opacity-60" />
          <ArticleTableOfContents :items="tocItems" title="Contents" />
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 min-h-screen">
        <article class="max-w-4xl space-y-12">
          <section id="overview" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="1" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">Overview</h2>
              <p class="content-prose">
                Our engineering achievements demonstrate the successful implementation of synthetic
                biology principles in creating a functional biosensor system.
              </p>
            </div>
          </section>

          <section id="key-achievements" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="2" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                Key Achievements
              </h2>

              <div class="space-y-8">
                <div>
                  <h3
                    id="genetic-circuit"
                    class="section-header text-xl font-serif-display text-slate-800 mt-8"
                  >
                    Genetic Circuit Design
                  </h3>
                  <p class="content-prose text-base">
                    Successfully designed and implemented genetic circuits with the following
                    features:
                  </p>
                  <ul class="technical-list mb-8">
                    <li class="technical-list-item group">
                      <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                        <ChevronRight class="w-5 h-5" />
                      </span>
                      <span
                        class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                      >
                        Modular promoter systems
                      </span>
                    </li>
                    <li class="technical-list-item group">
                      <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                        <ChevronRight class="w-5 h-5" />
                      </span>
                      <span
                        class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                      >
                        Efficient gene expression control
                      </span>
                    </li>
                    <li class="technical-list-item group">
                      <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                        <ChevronRight class="w-5 h-5" />
                      </span>
                      <span
                        class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                      >
                        Signal amplification mechanisms
                      </span>
                    </li>
                    <li class="technical-list-item group">
                      <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                        <ChevronRight class="w-5 h-5" />
                      </span>
                      <span
                        class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                      >
                        Feedback regulation systems
                      </span>
                    </li>
                  </ul>
                </div>

                <h3
                  id="protein-engineering"
                  class="section-header text-xl font-serif-display text-slate-800 mt-8"
                >
                  Protein Engineering
                </h3>
                <p class="content-prose text-base">
                  Achieved significant improvements in protein functionality:
                </p>
                <ul class="technical-list mb-8">
                  <li class="technical-list-item group">
                    <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                      <ChevronRight class="w-5 h-5" />
                    </span>
                    <span
                      class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                    >
                      Enhanced protein stability
                    </span>
                  </li>
                  <li class="technical-list-item group">
                    <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                      <ChevronRight class="w-5 h-5" />
                    </span>
                    <span
                      class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                    >
                      Improved substrate specificity
                    </span>
                  </li>
                  <li class="technical-list-item group">
                    <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                      <ChevronRight class="w-5 h-5" />
                    </span>
                    <span
                      class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                    >
                      Optimized catalytic efficiency
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </section>

          <section id="optimization" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="3" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">
                System Optimization
              </h2>
              <p class="content-prose">
                Our optimization efforts led to significant improvements in system performance:
              </p>
              <div class="bg-slate-900/5 border border-technical p-6 mb-6 overflow-x-auto">
                <KatexMath
                  expression="Efficiency = \frac{Output_{signal}}{Input_{substrate}} \times 100\%"
                  :display="true"
                  class="text-xl"
                />
              </div>
            </div>
          </section>

          <section id="validation" class="mb-12">
            <div class="card-blueprint card-content-spacing">
              <TechnicalElements type="number" :number="4" class="mb-4" />
              <h2 class="text-2xl mb-6 font-serif-display text-slate-800 font-light">Validation</h2>

              <h3
                id="performance-metrics"
                class="section-header text-xl font-serif-display text-slate-800 mt-8"
              >
                Performance Metrics
              </h3>
              <p class="content-prose text-base">
                Key performance indicators of our engineered system:
              </p>
              <ul class="technical-list mb-8">
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Response time: < 30 minutes
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Detection limit: 1 nM
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Dynamic range: 3 orders of magnitude
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Specificity: > 95%
                  </span>
                </li>
              </ul>

              <h3
                id="reproducibility"
                class="section-header text-xl font-serif-display text-slate-800 mt-8"
              >
                Reproducibility
              </h3>
              <p class="content-prose text-base">Our results have been validated through:</p>
              <ul class="technical-list mb-8">
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Multiple independent experiments
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Different laboratory conditions
                  </span>
                </li>
                <li class="technical-list-item group">
                  <span class="text-primary-600 mr-4 mt-0.5 flex-shrink-0">
                    <ChevronRight class="w-5 h-5" />
                  </span>
                  <span
                    class="text-slate-700 group-hover:text-slate-900 transition-colors duration-200 flex-1"
                  >
                    Various sample types
                  </span>
                </li>
              </ul>
            </div>
          </section>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import KatexMath from '@/components/KatexMath.vue'
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'
import TechnicalElements from '@/components/ui/TechnicalElements.vue'
import { ChevronRight } from 'lucide-vue-next'

// Define component name to satisfy ESLint multi-word component name rule
defineOptions({
  name: 'EngineeringSuccess',
})

const tocItems = ref([
  { title: 'Overview', url: '#overview', depth: 1 },
  { title: 'Key Achievements', url: '#key-achievements', depth: 1 },
  { title: 'Genetic Circuit Design', url: '#genetic-circuit', depth: 2 },
  { title: 'Protein Engineering', url: '#protein-engineering', depth: 2 },
  { title: 'System Optimization', url: '#optimization', depth: 1 },
  { title: 'Validation', url: '#validation', depth: 1 },
  { title: 'Performance Metrics', url: '#performance-metrics', depth: 2 },
  { title: 'Reproducibility', url: '#reproducibility', depth: 2 },
])
</script>

<style scoped>
.engineering-container {
  min-height: calc(100vh - 64px); /* Adjust based on your header height */
  background-color: rgb(249, 250, 251); /* bg-gray-50 */
  padding-top: 4rem; /* Increase top spacing to solve issue with navigation bar being too close */
  padding-bottom: 2rem;
}

@media (min-width: 640px) {
  .engineering-container {
    padding-top: 5rem; /* Increase top spacing for medium screens */
    padding-bottom: 3rem;
  }
}

@media (min-width: 768px) {
  .engineering-container {
    padding-top: 6rem; /* Increase top spacing for large screens */
    padding-bottom: 4rem;
  }
}
</style>
