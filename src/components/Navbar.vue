<template>
  <nav
    class="fixed w-full top-0 z-50 transition-all duration-200 ease-out"
    :class="[
      isScrolled
        ? 'bg-white/98 backdrop-blur-md shadow-technical border-b border-technical'
        : 'bg-white/95 backdrop-blur-sm border-b border-technical',
    ]"
  >
    <!-- Technical grid overlay for navbar -->
    <div class="absolute inset-0 bg-noise-texture opacity-20 pointer-events-none"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
      <div class="flex items-center justify-between h-16">
        <!-- Logo with Technical Frame -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center space-x-3 group">
            <div class="relative">
              <div
                class="w-10 h-10 overflow-hidden border border-technical transition-all duration-300 group-hover:border-primary-500"
              >
                <img
                  src="https://static.igem.wiki/teams/5610/wiki/icon/logo.webp"
                  alt="SnaPFAS Logo"
                  class="w-full h-full object-cover"
                />
              </div>
              <!-- Technical corner decoration -->
              <div
                class="absolute -top-1 -left-1 w-2 h-2 border-t border-l border-primary-600/40 group-hover:border-primary-600 transition-colors"
              ></div>
              <div
                class="absolute -top-1 -right-1 w-2 h-2 border-t border-r border-primary-600/40 group-hover:border-primary-600 transition-colors"
              ></div>
              <div
                class="absolute -bottom-1 -left-1 w-2 h-2 border-b border-l border-primary-600/40 group-hover:border-primary-600 transition-colors"
              ></div>
              <div
                class="absolute -bottom-1 -right-1 w-2 h-2 border-b border-r border-primary-600/40 group-hover:border-primary-600 transition-colors"
              ></div>
            </div>
            <div>
              <span
                class="font-serif-display text-xl text-slate-900 transition-colors duration-200 group-hover:text-primary-600"
              >
                SnaPFAS
              </span>
              <div class="text-[10px] font-mono uppercase tracking-wider text-slate-500 -mt-1">
                BASIS-CHINA 2025
              </div>
            </div>
          </router-link>
        </div>

        <!-- Desktop Navigation with Technical Style -->
        <div class="hidden md:flex items-center space-x-2">
          <router-link
            v-for="item in mainNavItems"
            :key="item.path"
            :to="item.path"
            class="relative px-4 py-2 text-sm font-sans-clean font-medium transition-all duration-200 group"
            :class="getLinkClass(item.path)"
          >
            <span class="relative z-10">{{ item.name }}</span>
            <!-- Technical hover effect -->
            <div
              class="absolute inset-0 border border-transparent group-hover:border-primary-500/30 transition-all duration-200"
            ></div>
          </router-link>

          <!-- Dropdowns with Technical Style -->
          <div v-for="dropdown in dropdownItems" :key="dropdown.key" class="relative group">
            <button
              class="relative px-4 py-2 text-sm font-sans-clean font-medium transition-all duration-200 flex items-center space-x-1"
              :class="getDropdownClass(dropdown.key)"
            >
              <span>{{ dropdown.name }}</span>
              <ChevronDown
                class="w-3 h-3 transition-transform duration-200 group-hover:rotate-180"
              />
              <!-- Technical hover effect -->
              <div
                class="absolute inset-0 border border-transparent group-hover:border-primary-500/30 transition-all duration-200"
              ></div>
            </button>

            <!-- Dropdown Menu with Blueprint Style -->
            <div
              class="absolute top-full left-0 mt-2 w-56 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 transform scale-95 group-hover:scale-100 origin-top"
            >
              <div class="bg-white border border-technical shadow-technical overflow-hidden">
                <!-- Technical decoration -->
                <div
                  class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary-500/20 to-transparent"
                ></div>

                <div class="p-2">
                  <router-link
                    v-for="item in dropdown.items"
                    :key="item.path"
                    :to="item.path"
                    class="block px-4 py-2.5 text-sm font-sans-clean text-slate-700 hover:bg-primary-50/50 hover:text-primary-700 transition-all duration-200 relative group/item"
                  >
                    <span class="flex items-center">
                      <ChevronRight
                        class="w-3 h-3 mr-2 opacity-0 group-hover/item:opacity-100 transition-opacity"
                      />
                      {{ item.name }}
                    </span>
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Mobile menu button with Technical Style -->
        <button
          @click="isMobileMenuOpen = !isMobileMenuOpen"
          class="md:hidden p-2 transition-all duration-200 text-slate-900 hover:text-primary-600 active:scale-95 border border-technical hover:border-primary-500"
        >
          <Menu v-if="!isMobileMenuOpen" class="w-5 h-5 transition-transform duration-300" />
          <X v-else class="w-5 h-5 transition-transform duration-300 rotate-90" />
        </button>
      </div>
    </div>

    <!-- Mobile menu with Blueprint Style -->
    <Transition
      enter-active-class="transition duration-300 ease-out"
      enter-from-class="transform scale-95 opacity-0"
      enter-to-class="transform scale-100 opacity-100"
      leave-active-class="transition duration-200 ease-in"
      leave-from-class="transform scale-100 opacity-100"
      leave-to-class="transform scale-95 opacity-0"
    >
      <div
        v-if="isMobileMenuOpen"
        class="md:hidden bg-white/98 backdrop-blur-md border-t border-technical shadow-technical relative"
      >
        <!-- Technical grid overlay -->
        <div class="absolute inset-0 bg-noise-texture opacity-10 pointer-events-none"></div>

        <div class="px-4 py-3 space-y-1 relative">
          <router-link
            v-for="item in mainNavItems"
            :key="item.path"
            :to="item.path"
            @click="isMobileMenuOpen = false"
            class="block px-4 py-2.5 text-base font-sans-clean font-medium text-slate-700 hover:bg-primary-50/50 hover:text-primary-700 transition-all duration-200 border-l-2 border-transparent hover:border-primary-500"
          >
            {{ item.name }}
          </router-link>

          <div v-for="dropdown in dropdownItems" :key="dropdown.key" class="space-y-1 pt-2">
            <div
              class="px-3 py-2 text-xs font-mono uppercase tracking-wider text-slate-500 border-t border-technical pt-3"
            >
              {{ dropdown.name }}
            </div>
            <router-link
              v-for="item in dropdown.items"
              :key="item.path"
              :to="item.path"
              @click="isMobileMenuOpen = false"
              class="block pl-8 pr-4 py-2 text-base font-sans-clean text-slate-600 hover:bg-primary-50/50 hover:text-primary-700 transition-all duration-200 border-l-2 border-transparent hover:border-primary-500"
            >
              {{ item.name }}
            </router-link>
          </div>
        </div>
      </div>
    </Transition>
  </nav>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { Menu, X, ChevronDown, ChevronRight } from 'lucide-vue-next'

const route = useRoute()
const isScrolled = ref(false)
const isMobileMenuOpen = ref(false)

const mainNavItems = [
  { name: 'Home', path: '/' },
  { name: 'Human Practices', path: '/human-practices' },
]

const dropdownItems = [
  {
    key: 'project',
    name: 'Project',
    items: [
      { name: 'Overview', path: '/project/overview' },
      { name: 'Design', path: '/project/design' },
    ],
  },
  {
    key: 'team',
    name: 'Team',
    items: [
      { name: 'Team Members', path: '/teammembers' },
      { name: 'Attribution', path: '/attribution' },
    ],
  },
  {
    key: 'wet-lab',
    name: 'Wet Lab',
    items: [
      { name: 'Experiment', path: '/wet-lab/experiment' },
      { name: 'Parts', path: '/wet-lab/parts' },
      { name: 'Results', path: '/wet-lab/results' },
      { name: 'Safety', path: '/wet-lab/safety' },
      { name: 'Engineering', path: '/wet-lab/engineering' },
    ],
  },
  {
    key: 'dry-lab',
    name: 'Dry Lab',
    items: [
      { name: 'Model', path: '/dry-lab/model' },
      { name: 'Hardware', path: '/dry-lab/hardware' },
      { name: 'Software', path: '/dry-lab/software' },
    ],
  },
]

const handleScroll = () => {
  isScrolled.value = window.scrollY > 10
}

const getLinkClass = (path: string) => {
  const isActive = route.path === path
  return isActive
    ? 'bg-primary-100 text-primary-700'
    : 'text-neutral-700 hover:bg-neutral-50 hover:text-neutral-900'
}

const getDropdownClass = (key: string) => {
  const isActive = dropdownItems
    .find(d => d.key === key)
    ?.items.some(item => route.path === item.path)

  return isActive
    ? 'bg-primary-100 text-primary-700'
    : 'text-neutral-700 hover:bg-neutral-50 hover:text-neutral-900'
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  handleScroll()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>
