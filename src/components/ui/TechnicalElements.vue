<template>
  <div>
    <!-- Technical Corner Bracket -->
    <svg
      v-if="type === 'corner-bracket'"
      :width="size"
      :height="size"
      viewBox="0 0 24 24"
      fill="none"
      :class="className"
    >
      <path
        d="M4 4h5M4 4v5"
        stroke="currentColor"
        stroke-width="1"
        stroke-linecap="round"
        stroke-linejoin="round"
        :class="animated ? 'animate-draw' : ''"
      />
    </svg>

    <!-- Technical Grid Dot -->
    <svg
      v-else-if="type === 'grid-dot'"
      :width="size"
      :height="size"
      viewBox="0 0 24 24"
      fill="none"
      :class="className"
    >
      <circle cx="12" cy="12" r="1.5" fill="currentColor" opacity="0.3" />
      <circle cx="12" cy="12" r="0.5" fill="currentColor" />
    </svg>

    <!-- Technical Cross -->
    <svg
      v-else-if="type === 'cross'"
      :width="size"
      :height="size"
      viewBox="0 0 24 24"
      fill="none"
      :class="className"
    >
      <path
        d="M12 2v20M2 12h20"
        stroke="currentColor"
        stroke-width="0.5"
        opacity="0.3"
        :class="animated ? 'animate-draw' : ''"
      />
    </svg>

    <!-- Technical Annotation -->
    <svg
      v-else-if="type === 'annotation'"
      :width="size * 2"
      :height="size"
      viewBox="0 0 48 24"
      fill="none"
      :class="className"
    >
      <path
        d="M2 12h44"
        stroke="currentColor"
        stroke-width="1"
        stroke-dasharray="2 2"
        opacity="0.3"
      />
      <circle cx="2" cy="12" r="2" fill="currentColor" opacity="0.5" />
      <circle cx="46" cy="12" r="2" fill="currentColor" opacity="0.5" />
    </svg>

    <!-- Technical Frame -->
    <svg
      v-else-if="type === 'frame'"
      :width="size * 2"
      :height="size * 2"
      viewBox="0 0 48 48"
      fill="none"
      :class="className"
    >
      <rect
        x="4"
        y="4"
        width="40"
        height="40"
        stroke="currentColor"
        stroke-width="1"
        fill="none"
        opacity="0.2"
      />
      <path
        d="M2 2h6v6M2 46h6v-6M46 2h-6v6M46 46h-6v-6"
        stroke="currentColor"
        stroke-width="1"
        fill="none"
      />
    </svg>

    <!-- Technical Divider -->
    <div v-else-if="type === 'divider'" :class="cn('divider-technical', className)" />

    <!-- Technical Number Circle -->
    <div
      v-else-if="type === 'number'"
      :class="cn('annotation-number', className)"
      :style="{ width: size + 'px', height: size + 'px' }"
    >
      <slot>{{ number }}</slot>
    </div>

    <!-- Grid Dots Pattern -->
    <div v-else-if="type === 'grid-dots'" :class="className">
      <svg :width="count * 8" height="8" viewBox="0 0 24 8" fill="none">
        <circle
          v-for="i in count"
          :key="i"
          :cx="(i - 1) * 8 + 4"
          cy="4"
          r="1.5"
          fill="currentColor"
        />
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils'

interface Props {
  type:
    | 'corner-bracket'
    | 'grid-dot'
    | 'cross'
    | 'annotation'
    | 'frame'
    | 'divider'
    | 'number'
    | 'grid-dots'
  size?: number
  className?: string
  animated?: boolean
  number?: number
  count?: number
}

withDefaults(defineProps<Props>(), {
  size: 24,
  className: '',
  animated: false,
  number: 1,
  count: 3,
})
</script>
