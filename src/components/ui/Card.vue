<template>
  <div
    :class="
      cn(
        cardVariants({ variant: props.variant, size: props.size, interactive: props.interactive }),
        $attrs.class ?? ''
      )
    "
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

const cardVariants = cva('rounded-2xl transition-all duration-300', {
  variants: {
    variant: {
      default: 'bg-white shadow-sm border border-neutral-100',
      elevated: 'bg-white shadow-lg hover:shadow-xl transform hover:-translate-y-1',
      glass:
        'bg-white/80 backdrop-blur-xl border border-white/20 shadow-lg bg-gradient-to-br from-white/90 to-white/70',
      bordered: 'bg-white border-2 border-neutral-200 hover:border-neutral-300',
      gradient:
        'bg-gradient-to-br from-white via-neutral-50 to-white shadow-md border border-neutral-100/50',
      surface: 'bg-neutral-50 border border-neutral-200/50',
    },
    size: {
      sm: 'p-4',
      default: 'p-6',
      lg: 'p-8',
      xl: 'p-10',
    },
    interactive: {
      true: 'cursor-pointer hover:shadow-lg active:scale-[0.99] hover:border-neutral-300',
      false: '',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'default',
    interactive: false,
  },
})

interface CardProps {
  variant?: VariantProps<typeof cardVariants>['variant']
  size?: VariantProps<typeof cardVariants>['size']
  interactive?: VariantProps<typeof cardVariants>['interactive']
}

const props = withDefaults(defineProps<CardProps>(), {
  variant: 'default',
  size: 'default',
  interactive: false,
})
</script>
