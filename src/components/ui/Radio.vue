<template>
  <label class="inline-flex items-start gap-3 cursor-pointer group">
    <div class="relative flex items-center justify-center">
      <input
        type="radio"
        :name="name"
        :value="value"
        :checked="modelValue === value"
        :disabled="disabled"
        :required="required"
        @change="$emit('update:modelValue', value)"
        class="sr-only"
      />

      <div :class="cn(radioClasses)">
        <div
          v-if="modelValue === value"
          class="w-2 h-2 bg-white rounded-full"
          :class="{ 'animate-scale-in': !disabled }"
        />
      </div>
    </div>

    <div class="flex-1">
      <span
        class="text-sm font-sans-clean text-slate-700 select-none"
        :class="{ 'text-slate-400': disabled }"
      >
        {{ label }}
      </span>

      <p v-if="hint" class="mt-1 text-xs font-sans-clean text-slate-500">
        {{ hint }}
      </p>
    </div>
  </label>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'

interface Props {
  modelValue?: string | number
  value: string | number
  name: string
  label: string
  hint?: string
  disabled?: boolean
  required?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  required: false,
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
}>()

const radioClasses = computed(() => {
  const base = `
    w-5 h-5
    border-2 rounded-full
    transition-all duration-200
    flex items-center justify-center
  `

  const states = {
    unchecked: 'bg-white border-technical group-hover:border-slate-400',
    checked: 'bg-primary-600 border-primary-600',
    disabled: 'bg-slate-100 border-slate-300 cursor-not-allowed',
  }

  const currentState = props.disabled
    ? states.disabled
    : props.modelValue === props.value
      ? states.checked
      : states.unchecked

  return cn(base, currentState)
})
</script>

<style scoped>
@keyframes scale-in {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-scale-in {
  animation: scale-in 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
</style>
