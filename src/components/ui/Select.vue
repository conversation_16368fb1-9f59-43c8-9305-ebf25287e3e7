<template>
  <div class="relative">
    <label
      v-if="label"
      :for="selectId"
      class="block text-sm font-sans-clean text-slate-700 mb-2"
      :class="{ 'text-red-600': error }"
    >
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>

    <div class="relative">
      <select
        :id="selectId"
        :value="modelValue"
        :disabled="disabled"
        :required="required"
        :class="cn(selectClasses, className)"
        @change="$emit('update:modelValue', $event.target.value)"
      >
        <option v-if="placeholder" value="" disabled>
          {{ placeholder }}
        </option>
        <option
          v-for="option in options"
          :key="option.value"
          :value="option.value"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </option>
      </select>

      <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none text-slate-400">
        <ChevronDown class="w-4 h-4" />
      </div>
    </div>

    <p v-if="hint && !error" class="mt-2 text-xs font-sans-clean text-slate-500">
      {{ hint }}
    </p>

    <p v-if="error" class="mt-2 text-xs font-sans-clean text-red-600 flex items-center gap-1">
      <AlertCircle class="w-3 h-3" />
      {{ error }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'
import { ChevronDown, AlertCircle } from 'lucide-vue-next'

interface Option {
  label: string
  value: string | number
  disabled?: boolean
}

interface Props {
  modelValue?: string | number
  options: Option[]
  label?: string
  placeholder?: string
  error?: string
  hint?: string
  disabled?: boolean
  required?: boolean
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  required: false,
  className: '',
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
}>()

const selectId = `select-${Math.random().toString(36).substr(2, 9)}`

const selectClasses = computed(() => {
  const base = `
    w-full px-4 py-3 pr-10
    bg-white
    border rounded-md
    font-sans-clean text-sm
    appearance-none
    cursor-pointer
    transition-all duration-200
    focus:outline-none
  `

  const states = {
    default: 'border-technical hover:border-slate-300 focus:border-primary-500 focus:shadow-focus',
    error: 'border-red-500 hover:border-red-600 focus:border-red-600 focus:shadow-focus-error',
    disabled: 'bg-slate-50 border-slate-200 cursor-not-allowed opacity-60',
  }

  const currentState = props.disabled
    ? states.disabled
    : props.error
      ? states.error
      : states.default

  return cn(base, currentState)
})
</script>
