<template>
  <div :class="cn(skeletonClasses, className)" :style="customStyle">
    <div class="skeleton-shimmer" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'

interface Props {
  variant?: 'text' | 'circular' | 'rectangular' | 'rounded'
  width?: string | number
  height?: string | number
  className?: string
  animate?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'text',
  animate: true,
})

const skeletonClasses = computed(() => {
  const base = 'relative overflow-hidden bg-slate-200/50'

  const variants = {
    text: 'h-4 w-full rounded',
    circular: 'rounded-full',
    rectangular: 'rounded-md',
    rounded: 'rounded-lg',
  }

  const animation = props.animate ? 'animate-pulse' : ''

  return cn(base, variants[props.variant], animation)
})

const customStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  }

  if (props.height) {
    style.height = typeof props.height === 'number' ? `${props.height}px` : props.height
  }

  // Set default dimensions for circular variant
  if (props.variant === 'circular' && !props.width && !props.height) {
    style.width = '40px'
    style.height = '40px'
  }

  return style
})
</script>

<style scoped>
.skeleton-shimmer {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
