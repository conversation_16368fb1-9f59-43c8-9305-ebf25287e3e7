<template>
  <component :is="as" :class="cn(titleVariants({ size }), 'text-neutral-900', $attrs.class ?? '')">
    <slot />
  </component>
</template>

<script setup lang="ts">
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

const titleVariants = cva('font-semibold leading-tight tracking-tight transition-colors', {
  variants: {
    size: {
      sm: 'text-lg',
      default: 'text-2xl',
      lg: 'text-3xl',
      xl: 'text-4xl',
    },
  },
  defaultVariants: {
    size: 'default',
  },
})

interface CardTitleProps extends VariantProps<typeof titleVariants> {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'div'
}

withDefaults(defineProps<CardTitleProps>(), {
  as: 'h3',
})
</script>
