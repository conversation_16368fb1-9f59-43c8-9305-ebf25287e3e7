<template>
  <div
    class="inline-flex items-center justify-center"
    :style="{ width: size + 'px', height: size + 'px' }"
  >
    <svg class="animate-spin" :width="size" :height="size" viewBox="0 0 24 24" fill="none">
      <!-- Technical blueprint style spinner -->
      <g v-if="variant === 'technical'">
        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1" opacity="0.1" />
        <path
          d="M12 2 A10 10 0 0 1 22 12"
          stroke="currentColor"
          stroke-width="1"
          stroke-linecap="round"
          class="text-primary-600"
        />
        <circle cx="12" cy="2" r="1" fill="currentColor" class="text-primary-600" />
      </g>

      <!-- Dots spinner -->
      <g v-else-if="variant === 'dots'">
        <circle cx="4" cy="12" r="2" fill="currentColor" opacity="0.25">
          <animate
            attributeName="opacity"
            values="0.25;1;0.25"
            dur="1.2s"
            repeatCount="indefinite"
            begin="0s"
          />
        </circle>
        <circle cx="12" cy="12" r="2" fill="currentColor" opacity="0.25">
          <animate
            attributeName="opacity"
            values="0.25;1;0.25"
            dur="1.2s"
            repeatCount="indefinite"
            begin="0.4s"
          />
        </circle>
        <circle cx="20" cy="12" r="2" fill="currentColor" opacity="0.25">
          <animate
            attributeName="opacity"
            values="0.25;1;0.25"
            dur="1.2s"
            repeatCount="indefinite"
            begin="0.8s"
          />
        </circle>
      </g>

      <!-- Grid spinner -->
      <g v-else-if="variant === 'grid'">
        <rect x="2" y="2" width="6" height="6" fill="currentColor" opacity="0.3">
          <animate
            attributeName="opacity"
            values="0.3;1;0.3"
            dur="1.6s"
            repeatCount="indefinite"
            begin="0s"
          />
        </rect>
        <rect x="9" y="2" width="6" height="6" fill="currentColor" opacity="0.3">
          <animate
            attributeName="opacity"
            values="0.3;1;0.3"
            dur="1.6s"
            repeatCount="indefinite"
            begin="0.2s"
          />
        </rect>
        <rect x="16" y="2" width="6" height="6" fill="currentColor" opacity="0.3">
          <animate
            attributeName="opacity"
            values="0.3;1;0.3"
            dur="1.6s"
            repeatCount="indefinite"
            begin="0.4s"
          />
        </rect>
        <rect x="2" y="9" width="6" height="6" fill="currentColor" opacity="0.3">
          <animate
            attributeName="opacity"
            values="0.3;1;0.3"
            dur="1.6s"
            repeatCount="indefinite"
            begin="0.6s"
          />
        </rect>
        <rect x="9" y="9" width="6" height="6" fill="currentColor" opacity="0.3">
          <animate
            attributeName="opacity"
            values="0.3;1;0.3"
            dur="1.6s"
            repeatCount="indefinite"
            begin="0.8s"
          />
        </rect>
        <rect x="16" y="9" width="6" height="6" fill="currentColor" opacity="0.3">
          <animate
            attributeName="opacity"
            values="0.3;1;0.3"
            dur="1.6s"
            repeatCount="indefinite"
            begin="1s"
          />
        </rect>
        <rect x="2" y="16" width="6" height="6" fill="currentColor" opacity="0.3">
          <animate
            attributeName="opacity"
            values="0.3;1;0.3"
            dur="1.6s"
            repeatCount="indefinite"
            begin="1.2s"
          />
        </rect>
        <rect x="9" y="16" width="6" height="6" fill="currentColor" opacity="0.3">
          <animate
            attributeName="opacity"
            values="0.3;1;0.3"
            dur="1.6s"
            repeatCount="indefinite"
            begin="1.4s"
          />
        </rect>
        <rect x="16" y="16" width="6" height="6" fill="currentColor" opacity="0.3">
          <animate
            attributeName="opacity"
            values="0.3;1;0.3"
            dur="1.6s"
            repeatCount="indefinite"
            begin="1.6s"
          />
        </rect>
      </g>

      <!-- Default circular spinner -->
      <g v-else>
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="2"
          fill="none"
          opacity="0.25"
        />
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="2"
          fill="none"
          stroke-dasharray="31.4 31.4"
          transform="rotate(-90 12 12)"
        />
      </g>
    </svg>
  </div>
</template>

<script setup lang="ts">
interface Props {
  size?: number
  variant?: 'default' | 'technical' | 'dots' | 'grid'
}

withDefaults(defineProps<Props>(), {
  size: 24,
  variant: 'technical',
})
</script>

<style scoped>
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
