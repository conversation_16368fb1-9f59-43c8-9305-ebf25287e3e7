<template>
  <div class="w-full overflow-hidden">
    <div class="overflow-x-auto">
      <table class="w-full min-w-full divide-y divide-technical">
        <thead class="bg-slate-50/50">
          <tr>
            <th
              v-for="(column, index) in columns"
              :key="column.key"
              :class="
                cn(
                  'px-6 py-4 text-left text-xs font-sans-clean font-medium text-slate-700 uppercase tracking-wider',
                  column.align === 'center' && 'text-center',
                  column.align === 'right' && 'text-right',
                  index === 0 && 'rounded-tl-md',
                  index === columns.length - 1 && 'rounded-tr-md'
                )
              "
              :style="column.width ? { width: column.width } : {}"
            >
              <div
                class="flex items-center gap-2"
                :class="{
                  'justify-center': column.align === 'center',
                  'justify-end': column.align === 'right',
                }"
              >
                {{ column.label }}
                <component
                  v-if="column.sortable"
                  :is="getSortIcon(column.key)"
                  class="w-4 h-4 text-slate-400 cursor-pointer hover:text-slate-600 transition-colors"
                  @click="handleSort(column.key)"
                />
              </div>
            </th>
          </tr>
        </thead>

        <tbody class="bg-white divide-y divide-technical">
          <tr
            v-for="(row, rowIndex) in sortedData"
            :key="row.id || rowIndex"
            class="hover:bg-slate-50/50 transition-colors duration-150"
          >
            <td
              v-for="column in columns"
              :key="column.key"
              :class="
                cn(
                  'px-6 py-4 whitespace-nowrap text-sm font-sans-clean text-slate-900',
                  column.align === 'center' && 'text-center',
                  column.align === 'right' && 'text-right'
                )
              "
            >
              <slot :name="`cell-${column.key}`" :row="row" :value="row[column.key]">
                {{ formatCellValue(row[column.key], column) }}
              </slot>
            </td>
          </tr>

          <tr v-if="data.length === 0">
            <td :colspan="columns.length" class="px-6 py-12 text-center text-sm text-slate-500">
              <div class="flex flex-col items-center gap-3">
                <Database class="w-12 h-12 text-slate-300" />
                <span class="font-sans-clean">No data available</span>
              </div>
            </td>
          </tr>
        </tbody>

        <tfoot v-if="showFooter" class="bg-slate-50/50">
          <tr>
            <td :colspan="columns.length" class="px-6 py-3 text-sm text-slate-600 font-sans-clean">
              <slot name="footer">
                <div class="flex items-center justify-between">
                  <span>Total: {{ data.length }} items</span>
                  <TechnicalElements type="annotation" :size="24" class="text-slate-300" />
                </div>
              </slot>
            </td>
          </tr>
        </tfoot>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { cn } from '@/lib/utils'
import { ArrowUpDown, ArrowUp, ArrowDown, Database } from 'lucide-vue-next'
import TechnicalElements from '@/components/ui/TechnicalElements.vue'

interface Column {
  key: string
  label: string
  sortable?: boolean
  align?: 'left' | 'center' | 'right'
  width?: string
  format?: (value: any) => string
}

interface Props {
  columns: Column[]
  data: Record<string, any>[]
  showFooter?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showFooter: false,
})

const sortKey = ref<string | null>(null)
const sortOrder = ref<'asc' | 'desc'>('asc')

const sortedData = computed(() => {
  if (!sortKey.value) return props.data

  return [...props.data].sort((a, b) => {
    const aVal = a[sortKey.value!]
    const bVal = b[sortKey.value!]

    if (aVal === bVal) return 0

    const order = sortOrder.value === 'asc' ? 1 : -1

    if (typeof aVal === 'string' && typeof bVal === 'string') {
      return aVal.localeCompare(bVal) * order
    }

    return (aVal > bVal ? 1 : -1) * order
  })
})

const handleSort = (key: string) => {
  if (sortKey.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortKey.value = key
    sortOrder.value = 'asc'
  }
}

const getSortIcon = (key: string) => {
  if (sortKey.value !== key) return ArrowUpDown
  return sortOrder.value === 'asc' ? ArrowUp : ArrowDown
}

const formatCellValue = (value: any, column: Column) => {
  if (column.format) {
    return column.format(value)
  }

  if (value === null || value === undefined) {
    return '-'
  }

  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No'
  }

  if (value instanceof Date) {
    return value.toLocaleDateString()
  }

  return String(value)
}
</script>

<style scoped>
.divide-technical > :not([hidden]) ~ :not([hidden]) {
  border-color: rgba(0, 0, 0, 0.06);
}
</style>
