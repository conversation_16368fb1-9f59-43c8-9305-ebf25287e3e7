<template>
  <div class="relative">
    <label
      v-if="label"
      :for="inputId"
      class="block text-sm font-sans-clean text-slate-700 mb-2"
      :class="{ 'text-red-600': error }"
    >
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>

    <div class="relative">
      <div
        v-if="icon"
        class="absolute left-3 top-1/2 -translate-y-1/2 text-slate-400 pointer-events-none"
      >
        <component :is="icon" class="w-4 h-4" />
      </div>

      <input
        :id="inputId"
        :value="modelValue"
        :type="type"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :required="required"
        :class="cn(inputClasses, className)"
        @input="$emit('update:modelValue', $event.target.value)"
        @focus="focused = true"
        @blur="focused = false"
      />

      <div
        v-if="showClearButton"
        class="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer text-slate-400 hover:text-slate-600 transition-colors"
        @click="$emit('update:modelValue', '')"
      >
        <X class="w-4 h-4" />
      </div>
    </div>

    <p v-if="hint && !error" class="mt-2 text-xs font-sans-clean text-slate-500">
      {{ hint }}
    </p>

    <p v-if="error" class="mt-2 text-xs font-sans-clean text-red-600 flex items-center gap-1">
      <AlertCircle class="w-3 h-3" />
      {{ error }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { cn } from '@/lib/utils'
import { X, AlertCircle } from 'lucide-vue-next'

interface Props {
  modelValue?: string | number
  label?: string
  placeholder?: string
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search'
  icon?: any
  error?: string
  hint?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  clearable?: boolean
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  disabled: false,
  readonly: false,
  required: false,
  clearable: false,
  className: '',
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
}>()

const focused = ref(false)
const inputId = `input-${Math.random().toString(36).substr(2, 9)}`

const showClearButton = computed(() => {
  return props.clearable && props.modelValue && !props.disabled && !props.readonly
})

const inputClasses = computed(() => {
  const base = `
    w-full px-4 py-3 
    bg-white
    border rounded-md
    font-sans-clean text-sm
    transition-all duration-200
    focus:outline-none
  `

  const iconPadding = props.icon ? 'pl-10' : ''
  const clearPadding = props.clearable ? 'pr-10' : ''

  const states = {
    default: 'border-technical hover:border-slate-300 focus:border-primary-500 focus:shadow-focus',
    error: 'border-red-500 hover:border-red-600 focus:border-red-600 focus:shadow-focus-error',
    disabled: 'bg-slate-50 border-slate-200 cursor-not-allowed opacity-60',
  }

  const currentState = props.disabled
    ? states.disabled
    : props.error
      ? states.error
      : states.default

  return cn(base, iconPadding, clearPadding, currentState)
})
</script>
