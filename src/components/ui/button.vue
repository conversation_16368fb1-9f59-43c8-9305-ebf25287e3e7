<template>
  <component
    :is="props.as"
    :class="cn(buttonVariants({ variant: props.variant, size: props.size }), props.class)"
    v-bind="$attrs"
  >
    <slot />
  </component>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils'
import { cva } from 'class-variance-authority'

const buttonVariants = cva(
  'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-300 ease-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-600/30 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 disabled:cursor-not-allowed [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 active:scale-[0.98] active:transition-transform active:duration-100',
  {
    variants: {
      variant: {
        default:
          'bg-gradient-to-br from-primary-600 via-primary-600 to-primary-700 text-white shadow-lg shadow-primary-600/20 hover:shadow-xl hover:shadow-primary-600/30 hover:from-primary-700 hover:via-primary-700 hover:to-primary-800 hover:-translate-y-0.5 transform will-change-transform',
        destructive:
          'bg-gradient-to-br from-red-600 via-red-600 to-red-700 text-white shadow-lg shadow-red-600/20 hover:shadow-xl hover:shadow-red-600/30 hover:from-red-700 hover:via-red-700 hover:to-red-800 hover:-translate-y-0.5',
        outline:
          'border-2 border-neutral-200 bg-white/80 backdrop-blur-sm text-neutral-700 hover:bg-neutral-50 hover:border-neutral-300 hover:shadow-lg hover:-translate-y-0.5 hover:text-neutral-900',
        secondary:
          'bg-gradient-to-br from-neutral-100 via-neutral-50 to-neutral-100 text-neutral-700 shadow-md hover:shadow-lg hover:from-neutral-200 hover:via-neutral-100 hover:to-neutral-200 hover:-translate-y-0.5',
        ghost: 'text-neutral-700 hover:bg-neutral-100/80 hover:text-neutral-900 hover:shadow-sm',
        glass:
          'bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 shadow-lg shadow-black/10 hover:shadow-xl hover:border-white/30 hover:-translate-y-0.5',
        'gradient-outline':
          'relative bg-white text-neutral-900 shadow-lg hover:shadow-xl hover:-translate-y-0.5 before:absolute before:inset-0 before:-z-10 before:rounded-lg before:bg-gradient-to-br before:from-primary-600 before:via-tertiary-600 before:to-secondary-600 before:p-[2px] before:transition-all before:duration-300 hover:before:p-[3px]',
        minimal: 'text-neutral-600 hover:text-neutral-900 hover:bg-neutral-50 rounded-md',
        technical: 'btn-technical',
      },
      size: {
        default: 'h-10 px-5 py-2 text-sm',
        sm: 'h-8 rounded-md px-3 text-xs',
        lg: 'h-12 rounded-lg px-8 text-base',
        xl: 'h-14 rounded-xl px-10 text-lg font-semibold',
        icon: 'h-10 w-10 p-0',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

interface Props {
  as?: string
  variant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'glass'
    | 'gradient-outline'
    | 'minimal'
    | 'technical'
  size?: 'default' | 'sm' | 'lg' | 'xl' | 'icon'
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  as: 'button',
  variant: 'default',
  size: 'default',
})
</script>
