<template>
  <span ref="elementRef">{{ displayValue }}</span>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useIntersectionObserver } from '@vueuse/core'

interface Props {
  value: number
  duration?: number
  prefix?: string
  suffix?: string
  separator?: string
}

const props = withDefaults(defineProps<Props>(), {
  duration: 2000,
  prefix: '',
  suffix: '',
  separator: ',',
})

const displayValue = ref(props.prefix + '0' + props.suffix)
const elementRef = ref<HTMLElement>()
const hasAnimated = ref(false)

const formatNumber = (num: number): string => {
  if (props.separator) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, props.separator)
  }
  return num.toString()
}

const animateValue = () => {
  if (hasAnimated.value) return
  hasAnimated.value = true

  const startTime = Date.now()
  const startValue = 0
  const endValue = props.value

  const updateNumber = () => {
    const currentTime = Date.now()
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / props.duration, 1)

    // Easing function for smooth animation
    const easeOutQuart = 1 - Math.pow(1 - progress, 4)
    const currentValue = Math.floor(startValue + (endValue - startValue) * easeOutQuart)

    displayValue.value = props.prefix + formatNumber(currentValue) + props.suffix

    if (progress < 1) {
      requestAnimationFrame(updateNumber)
    } else {
      displayValue.value = props.prefix + formatNumber(endValue) + props.suffix
    }
  }

  updateNumber()
}

useIntersectionObserver(
  elementRef,
  ([{ isIntersecting }]) => {
    if (isIntersecting) {
      animateValue()
    }
  },
  { threshold: 0.5 }
)

defineExpose({ elementRef })

onMounted(() => {
  // Set initial value without animation
  displayValue.value = props.prefix + '0' + props.suffix
})
</script>
