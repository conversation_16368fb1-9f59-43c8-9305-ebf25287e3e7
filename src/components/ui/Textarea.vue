<template>
  <div class="relative">
    <label
      v-if="label"
      :for="textareaId"
      class="block text-sm font-sans-clean text-slate-700 mb-2"
      :class="{ 'text-red-600': error }"
    >
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>

    <div class="relative">
      <textarea
        :id="textareaId"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :required="required"
        :rows="rows"
        :maxlength="maxlength"
        :class="cn(textareaClasses, className)"
        @input="$emit('update:modelValue', $event.target.value)"
        @focus="focused = true"
        @blur="focused = false"
      />

      <div v-if="maxlength" class="absolute bottom-2 right-3 text-xs text-slate-400">
        {{ modelValue?.toString().length || 0 }}/{{ maxlength }}
      </div>
    </div>

    <p v-if="hint && !error" class="mt-2 text-xs font-sans-clean text-slate-500">
      {{ hint }}
    </p>

    <p v-if="error" class="mt-2 text-xs font-sans-clean text-red-600 flex items-center gap-1">
      <AlertCircle class="w-3 h-3" />
      {{ error }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { cn } from '@/lib/utils'
import { AlertCircle } from 'lucide-vue-next'

interface Props {
  modelValue?: string
  label?: string
  placeholder?: string
  error?: string
  hint?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  rows?: number
  maxlength?: number
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  readonly: false,
  required: false,
  rows: 4,
  className: '',
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const focused = ref(false)
const textareaId = `textarea-${Math.random().toString(36).substr(2, 9)}`

const textareaClasses = computed(() => {
  const base = `
    w-full px-4 py-3
    bg-white
    border rounded-md
    font-sans-clean text-sm
    resize-none
    transition-all duration-200
    focus:outline-none
  `

  const maxlengthPadding = props.maxlength ? 'pb-8' : ''

  const states = {
    default: 'border-technical hover:border-slate-300 focus:border-primary-500 focus:shadow-focus',
    error: 'border-red-500 hover:border-red-600 focus:border-red-600 focus:shadow-focus-error',
    disabled: 'bg-slate-50 border-slate-200 cursor-not-allowed opacity-60',
  }

  const currentState = props.disabled
    ? states.disabled
    : props.error
      ? states.error
      : states.default

  return cn(base, maxlengthPadding, currentState)
})
</script>
