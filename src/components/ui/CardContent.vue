<template>
  <div :class="cn(contentPadding, $attrs.class ?? '')">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'

interface CardContentProps {
  noPadding?: boolean
  noTopPadding?: boolean
  compact?: boolean
}

const props = withDefaults(defineProps<CardContentProps>(), {
  noPadding: false,
  noTopPadding: true,
  compact: false,
})

const contentPadding = computed(() => {
  if (props.noPadding) return ''

  const basePadding = props.compact ? 'px-4 py-3' : 'px-6 py-5'

  if (props.noTopPadding) {
    return props.compact ? 'px-4 pb-3 pt-0' : 'px-6 pb-5 pt-0'
  }

  return basePadding
})
</script>
