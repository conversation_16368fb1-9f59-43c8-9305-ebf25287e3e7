<template>
  <div class="article-toc-container">
    <!-- Mobile dropdown menu (visible on small screens) -->
    <div class="block md:hidden mb-6">
      <div
        class="bg-white rounded-lg shadow-sm border border-slate-200 p-4 cursor-pointer hover:shadow-md transition-all duration-200"
        @click="mobileNavOpen = !mobileNavOpen"
      >
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-slate-900">
            {{ title }}
          </h2>
          <ChevronDown
            class="w-5 h-5 text-slate-400 transform transition-transform duration-200"
            :class="{ 'rotate-180': mobileNavOpen }"
          />
        </div>
      </div>

      <transition
        enter-active-class="transition-all duration-200 ease-out"
        enter-from-class="opacity-0 -translate-y-2"
        enter-to-class="opacity-100 translate-y-0"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="opacity-100 translate-y-0"
        leave-to-class="opacity-0 -translate-y-2"
      >
        <div
          v-if="mobileNavOpen"
          class="mt-2 bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden"
        >
          <nav class="p-4">
            <ul class="space-y-1">
              <li v-for="(item, index) in items" :key="index">
                <a
                  :href="item.url"
                  class="block py-2.5 px-4 text-sm rounded-md transition-all duration-200 relative"
                  :class="{
                    'bg-primary-50 text-primary-700 font-medium': isActive(item.url),
                    'text-slate-600 hover:text-slate-900 hover:bg-slate-50': !isActive(item.url),
                    'pl-8': item.depth === 2,
                    'pl-12': item.depth === 3,
                  }"
                  @click="handleItemClick"
                >
                  {{ item.title }}
                </a>
              </li>
            </ul>
          </nav>
        </div>
      </transition>
    </div>

    <!-- Desktop sidebar (visible on medium screens and above) -->
    <div class="hidden md:block w-full max-w-xs">
      <div class="bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden">
        <!-- Header -->
        <div class="px-5 py-4 border-b border-slate-200 bg-slate-50/50">
          <h2 class="text-base font-medium text-slate-900">
            {{ title }}
          </h2>
        </div>

        <!-- Navigation -->
        <nav class="p-3" role="navigation" aria-label="Table of contents">
          <ul class="space-y-1">
            <li v-for="(item, index) in items" :key="index">
              <a
                :href="item.url"
                class="block py-2 px-3 text-sm rounded-md transition-all duration-200 relative group"
                :class="{
                  'bg-primary-50 text-primary-700 font-medium': isActive(item.url),
                  'text-slate-600 hover:text-slate-900 hover:bg-slate-50': !isActive(item.url),
                }"
                :style="{
                  paddingLeft: item.depth === 1 ? '12px' : item.depth === 2 ? '32px' : '52px',
                }"
                @click="handleItemClick"
              >
                <span class="relative flex items-center">
                  <!-- Active indicator -->
                  <span
                    v-if="isActive(item.url)"
                    class="absolute -left-3 w-0.5 h-full bg-primary-600 rounded-r"
                  />

                  <!-- Depth indicators -->
                  <span v-if="item.depth > 1" class="absolute left-0 text-slate-300 select-none">
                    {{ item.depth === 2 ? '–' : '—' }}
                  </span>

                  <!-- Title -->
                  <span class="relative">
                    {{ item.title }}
                  </span>
                </span>
              </a>
            </li>
          </ul>
        </nav>

        <!-- Progress bar -->
        <div class="h-0.5 bg-slate-100">
          <div
            class="h-full bg-primary-600 transition-all duration-300 ease-out"
            :style="{ width: scrollProgress + '%' }"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import { ChevronDown } from 'lucide-vue-next'

interface TocItem {
  title: string
  url: string
  depth: number
}

interface ArticleTocProps {
  items: TocItem[]
  title?: string
  defaultOpen?: boolean
}

const props = withDefaults(defineProps<ArticleTocProps>(), {
  items: () => [],
  title: 'Table of Contents',
  defaultOpen: false,
})

const mobileNavOpen = ref(props.defaultOpen)
const activeSection = ref('')
const scrollProgress = ref(0)

// Check if link is active
const isActive = (url: string): boolean => {
  return activeSection.value === url.replace('#', '')
}

// Handle click events
const handleItemClick = (): void => {
  // Close menu on mobile
  if (window.innerWidth < 768) {
    setTimeout(() => {
      mobileNavOpen.value = false
    }, 300)
  }
}

// Calculate scroll progress
const updateScrollProgress = (): void => {
  const winScroll = document.body.scrollTop || document.documentElement.scrollTop
  const height = document.documentElement.scrollHeight - document.documentElement.clientHeight
  scrollProgress.value = (winScroll / height) * 100
}

// Listen to scroll to update active section
const updateActiveSection = (): void => {
  const sections = props.items
    .map(item => {
      const id = item.url.replace('#', '')
      const element = document.getElementById(id)
      if (element) {
        const rect = element.getBoundingClientRect()
        return {
          id,
          top: rect.top,
          bottom: rect.bottom,
        }
      }
      return null
    })
    .filter(Boolean) as { id: string; top: number; bottom: number }[]

  // Find the section currently in viewport
  const viewportHeight = window.innerHeight
  const currentSection = sections.find(section => {
    return section.top <= viewportHeight * 0.3 && section.bottom >= 0
  })

  if (currentSection) {
    activeSection.value = currentSection.id
  }

  updateScrollProgress()
}

// Debounce function
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout> | null = null
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      func(...args)
    }
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(later, wait)
  }
}

const debouncedUpdateActiveSection = debounce(updateActiveSection, 50)

onMounted(() => {
  window.addEventListener('scroll', debouncedUpdateActiveSection)
  updateActiveSection()
})

onUnmounted(() => {
  window.removeEventListener('scroll', debouncedUpdateActiveSection)
})
</script>

<style scoped>
.article-toc-container {
  position: relative;
}

@media (min-width: 768px) {
  .article-toc-container {
    position: sticky;
    top: 2rem;
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
    overflow-x: hidden;
  }
}

/* Custom scrollbar */
.article-toc-container::-webkit-scrollbar {
  width: 6px;
}

.article-toc-container::-webkit-scrollbar-track {
  background: transparent;
}

.article-toc-container::-webkit-scrollbar-thumb {
  background: theme('colors.slate.300');
  border-radius: 3px;
}

.article-toc-container::-webkit-scrollbar-thumb:hover {
  background: theme('colors.slate.400');
}

/* Firefox scrollbar */
.article-toc-container {
  scrollbar-width: thin;
  scrollbar-color: theme('colors.slate.300') transparent;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles */
a:focus-visible {
  outline: 2px solid theme('colors.primary.400');
  outline-offset: -2px;
  border-radius: 6px;
}
</style>
