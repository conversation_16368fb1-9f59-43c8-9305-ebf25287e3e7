<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Convert OG Image to PNG</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            padding: 2rem;
            background: #f3f4f6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .instructions {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        h1 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        button {
            background: #3b82f6;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.375rem;
            font-size: 1rem;
            cursor: pointer;
            margin-right: 1rem;
        }
        button:hover {
            background: #2563eb;
        }
        #canvas {
            border: 1px solid #e5e7eb;
            display: none;
        }
        .preview {
            background: #e5e7eb;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
        }
        #svgContainer {
            display: inline-block;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="instructions">
            <h1>OpenGraph Image Converter</h1>
            <p>This page converts the SVG OpenGraph image to PNG format.</p>
            <button onclick="convertToPNG()">Convert to PNG</button>
            <button onclick="downloadPNG()" id="downloadBtn" style="display:none;">Download PNG</button>
        </div>
        
        <div class="preview">
            <h2>Preview:</h2>
            <div id="svgContainer"></div>
        </div>
        
        <canvas id="canvas" width="1200" height="630"></canvas>
    </div>

    <script>
        // Load and display the SVG
        fetch('/basis-china/og-image.svg')
            .then(response => response.text())
            .then(svg => {
                document.getElementById('svgContainer').innerHTML = svg;
            });

        function convertToPNG() {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // Create an image from the SVG
            const svgData = document.querySelector('#svgContainer svg').outerHTML;
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);
            
            const img = new Image();
            img.onload = function() {
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, 1200, 630);
                ctx.drawImage(img, 0, 0);
                URL.revokeObjectURL(url);
                
                // Show download button
                document.getElementById('downloadBtn').style.display = 'inline-block';
                alert('Conversion complete! Click "Download PNG" to save the image.');
            };
            img.src = url;
        }
        
        function downloadPNG() {
            const canvas = document.getElementById('canvas');
            const link = document.createElement('a');
            link.download = 'og-image.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
    </script>
</body>
</html>